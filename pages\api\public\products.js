import { supabase } from '@/lib/supabase';

/**
 * Public API endpoint for fetching products
 * This endpoint provides products data for the public shop
 * No authentication required - only returns active products
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { category, featured } = req.query;

  try {
    // Build query for active products
    // Use both status and is_active fields for compatibility
    let query = supabase
      .from('products')
      .select('*')
      .or('status.eq.active,and(status.is.null,is_active.eq.true)');

    // Apply filters if provided
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (featured === 'true') {
      query = query.eq('featured', true);
    }

    // Order by featured first, then by name
    query = query.order('featured', { ascending: false })
                 .order('name');

    const { data: products, error } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return res.status(500).json({ error: 'Failed to fetch products' });
    }

    // Transform database products to match the format expected by the frontend
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description || '',
      price: parseFloat(product.price) || 0,
      salePrice: product.sale_price ? parseFloat(product.sale_price) : null,
      image: product.image_url || '/images/products/default-product.jpg',
      additionalImage: product.gallery_images && product.gallery_images.length > 0
        ? product.gallery_images[0]
        : '',
      category: product.category || 'general',
      features: generateFeatures(product),
      details: generateDetails(product),
      badge: getBadge(product),
      stock: product.stock || 0,
      sku: product.sku || '',
      featured: product.featured || false
    }));

    return res.status(200).json({
      products: transformedProducts,
      total: transformedProducts.length
    });

  } catch (error) {
    console.error('Error in products API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Generate product features based on category and properties
 */
function generateFeatures(product) {
  const baseFeatures = [
    'High-Quality Formula',
    'Skin Safe',
    'Cruelty-Free & Vegan-Friendly'
  ];

  // Add category-specific features
  if (product.category === 'uv-products') {
    baseFeatures.unshift('UV-Reactive Formula');
  }

  if (product.category === 'split-cakes') {
    baseFeatures.unshift('Smudge-proof & Transfer-resistant');
    baseFeatures.unshift('Strong Pigment Shades');
    baseFeatures.unshift('Blendable Formula');
  }

  return baseFeatures;
}

/**
 * Generate product details based on category and properties
 */
function generateDetails(product) {
  const details = [];

  // Add weight/volume info based on category
  if (product.category === 'split-cakes') {
    details.push('Product Volume: 60g split cake');
  } else if (product.category === 'uv-products') {
    details.push('Net Weight: 40g (8 x 5g each)');
    details.push('Total Weight: 120g');
  }

  details.push('Water-Based Formula');
  details.push('Not waterproof');

  return details;
}

/**
 * Determine product badge based on properties
 */
function getBadge(product) {
  if (product.featured) {
    return 'Featured';
  }

  // Check if product was created recently (within last 30 days)
  const createdDate = new Date(product.created_at);
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  if (createdDate > thirtyDaysAgo) {
    return 'New Arrival';
  }

  if (product.sale_price && product.sale_price < product.price) {
    return 'Sale';
  }

  return null;
}
