/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['www.oceansoulsparkles.com.au', 'static.wixstatic.com'],
  },
  // Configure CORS for API routes
  async headers() {
    return [
      {
        // Apply these headers to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'development' ? '*' : 'https://www.oceansoulsparkles.com.au',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, X-Client-Info, X-Auth-Token, X-OneSignal-Client',
          },
        ],
      },
    ];
  },
  compiler: {
    // Enable all experimental features
    styledComponents: true, // Enable CSS-in-JS
    emotion: true,
  },
  webpack: (config, { isServer }) => {
    // Add support for importing SVGs as React components
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    if (!isServer) {
      // Don't attempt to import Node.js built-in modules on the client side
      config.resolve.fallback = {
        fs: false,
        path: false,
        util: false,
        crypto: false,
      };
    }

    return config;
  },
}

export default nextConfig;
