#!/usr/bin/env node

/**
 * Test Admin Panel Access Script
 *
 * This script tests the admin panel API endpoints to verify that the data migration
 * is accessible and the admin panel is working correctly.
 */

// Use dynamic import for node-fetch
let fetch;

const BASE_URL = 'http://localhost:3001';

// Test configuration
const TEST_CONFIG = {
  // Test endpoints
  endpoints: [
    '/api/admin/customers?limit=5',
    '/api/admin/customers?limit=1', // For stats
    '/api/public/customers', // Public endpoint test
  ],

  // Headers for authenticated requests
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Admin-Panel-Test/1.0'
  }
};

/**
 * Test an API endpoint
 */
async function testEndpoint(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;

  console.log(`\n🔍 Testing: ${endpoint}`);
  console.log(`   URL: ${url}`);

  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        ...TEST_CONFIG.headers,
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });

    const contentType = response.headers.get('content-type');
    let data;

    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Content-Type: ${contentType}`);

    if (response.ok) {
      console.log(`   ✅ Success`);

      // Log data structure for analysis
      if (typeof data === 'object' && data !== null) {
        if (Array.isArray(data)) {
          console.log(`   📊 Data: Array with ${data.length} items`);
          if (data.length > 0) {
            console.log(`   📝 Sample item keys: ${Object.keys(data[0]).join(', ')}`);
          }
        } else {
          console.log(`   📊 Data: Object with keys: ${Object.keys(data).join(', ')}`);

          // Special handling for customer data
          if (data.customers && Array.isArray(data.customers)) {
            console.log(`   👥 Customers: ${data.customers.length} records`);
            console.log(`   📈 Total: ${data.total || 'not specified'}`);
          }
        }
      } else {
        console.log(`   📊 Data: ${typeof data} - ${String(data).substring(0, 100)}...`);
      }
    } else {
      console.log(`   ❌ Error: ${response.status}`);
      console.log(`   📄 Response: ${JSON.stringify(data, null, 2)}`);
    }

    return { success: response.ok, status: response.status, data };

  } catch (error) {
    console.log(`   💥 Network Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test database connectivity via Supabase
 */
async function testDatabaseConnectivity() {
  console.log(`\n🗄️  Testing Database Connectivity`);

  // Test a simple health check endpoint
  return await testEndpoint('/api/health');
}

/**
 * Test customer data access
 */
async function testCustomerAccess() {
  console.log(`\n👥 Testing Customer Data Access`);

  const results = [];

  // Test different customer endpoints
  for (const endpoint of TEST_CONFIG.endpoints) {
    if (endpoint.includes('customers')) {
      const result = await testEndpoint(endpoint);
      results.push({ endpoint, result });
    }
  }

  return results;
}

/**
 * Test other migrated data
 */
async function testOtherData() {
  console.log(`\n📊 Testing Other Migrated Data`);

  const endpoints = [
    '/api/admin/products?limit=5',
    '/api/admin/invoices?limit=5',
    '/api/admin/marketing/campaigns?limit=5'
  ];

  const results = [];

  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ endpoint, result });
  }

  return results;
}

/**
 * Main test runner
 */
async function runTests() {
  // Initialize fetch
  if (!fetch) {
    const { default: nodeFetch } = await import('node-fetch');
    fetch = nodeFetch;
  }

  console.log('🌊 Ocean Soul Sparkles - Admin Panel Access Test');
  console.log('================================================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);

  const testResults = {
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    tests: {}
  };

  try {
    // Test 1: Database connectivity
    testResults.tests.database = await testDatabaseConnectivity();

    // Test 2: Customer data access
    testResults.tests.customers = await testCustomerAccess();

    // Test 3: Other migrated data
    testResults.tests.otherData = await testOtherData();

    // Summary
    console.log(`\n📋 Test Summary`);
    console.log('===============');

    let totalTests = 0;
    let passedTests = 0;

    // Count results
    Object.values(testResults.tests).forEach(testGroup => {
      if (Array.isArray(testGroup)) {
        testGroup.forEach(test => {
          totalTests++;
          if (test.result && test.result.success) passedTests++;
        });
      } else {
        totalTests++;
        if (testGroup && testGroup.success) passedTests++;
      }
    });

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
      console.log(`\n🎉 All tests passed! Admin panel should be working correctly.`);
    } else {
      console.log(`\n⚠️  Some tests failed. Check the errors above for details.`);
    }

  } catch (error) {
    console.error(`\n💥 Test runner error: ${error.message}`);
    testResults.error = error.message;
  }

  // Save results to file
  const fs = require('fs');
  const resultsFile = `test-results/admin-panel-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;

  try {
    if (!fs.existsSync('test-results')) {
      fs.mkdirSync('test-results');
    }
    fs.writeFileSync(resultsFile, JSON.stringify(testResults, null, 2));
    console.log(`\n📁 Results saved to: ${resultsFile}`);
  } catch (saveError) {
    console.error(`Failed to save results: ${saveError.message}`);
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testEndpoint, runTests };
