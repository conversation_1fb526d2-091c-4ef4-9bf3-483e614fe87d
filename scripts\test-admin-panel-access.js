#!/usr/bin/env node

/**
 * Admin Panel Access Test Script
 *
 * This script tests admin panel page access and functionality:
 * 1. Tests if admin pages are accessible
 * 2. Verifies component loading
 * 3. Checks for any critical errors
 */

// Load environment variables from .env.local
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: '.env.local' });

async function testAdminPanelAccess() {
  console.log('🔐 Starting Admin Panel Access Tests...\n');

  const results = {
    pageFiles: { status: '❌', details: [] },
    components: { status: '❌', details: [] },
    apiEndpoints: { status: '❌', details: [] },
    configuration: { status: '❌', details: [] }
  };

  try {
    // 1. Check Admin Page Files
    console.log('📄 Checking admin page files...');

    const adminPages = [
      'pages/admin/index.js',
      'pages/admin/customers/index.js',
      'pages/admin/bookings/index.js',
      'pages/admin/inventory/index.js'
    ];

    let pagesExist = 0;
    for (const pagePath of adminPages) {
      if (fs.existsSync(pagePath)) {
        pagesExist++;
        results.pageFiles.details.push(`✅ ${pagePath} exists`);
      } else {
        results.pageFiles.details.push(`❌ ${pagePath} missing`);
      }
    }

    results.pageFiles.status = pagesExist === adminPages.length ? '✅' : '⚠️';

    // 2. Check Admin Components
    console.log('🧩 Checking admin components...');

    const adminComponents = [
      'components/admin/CustomerList.js',
      'components/admin/BookingCalendar.js',
      'components/admin/ProductList.js',
      'components/admin/AdminLayout.js',
      'components/admin/ProtectedRoute.js'
    ];

    let componentsExist = 0;
    for (const componentPath of adminComponents) {
      if (fs.existsSync(componentPath)) {
        componentsExist++;
        results.components.details.push(`✅ ${componentPath} exists`);
      } else {
        results.components.details.push(`❌ ${componentPath} missing`);
      }
    }

    results.components.status = componentsExist === adminComponents.length ? '✅' : '⚠️';

    // 3. Check API Endpoints
    console.log('🔌 Checking API endpoints...');

    const apiEndpoints = [
      'pages/api/admin/customers/index.js',
      'pages/api/admin/bookings/index.js',
      'pages/api/admin/inventory/products.js'
    ];

    let endpointsExist = 0;
    for (const endpointPath of apiEndpoints) {
      if (fs.existsSync(endpointPath)) {
        endpointsExist++;
        results.apiEndpoints.details.push(`✅ ${endpointPath} exists`);
      } else {
        results.apiEndpoints.details.push(`❌ ${endpointPath} missing`);
      }
    }

    results.apiEndpoints.status = endpointsExist === apiEndpoints.length ? '✅' : '⚠️';

    // 4. Check Configuration
    console.log('⚙️ Checking configuration...');

    // Check environment variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY'
    ];

    let envVarsSet = 0;
    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        envVarsSet++;
        results.configuration.details.push(`✅ ${envVar} is set`);
      } else {
        results.configuration.details.push(`❌ ${envVar} missing`);
      }
    }

    // Check package.json for required dependencies
    if (fs.existsSync('package.json')) {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const requiredDeps = ['@supabase/supabase-js', 'react', 'next'];

      let depsInstalled = 0;
      for (const dep of requiredDeps) {
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          depsInstalled++;
          results.configuration.details.push(`✅ ${dep} dependency installed`);
        } else {
          results.configuration.details.push(`❌ ${dep} dependency missing`);
        }
      }
    }

    results.configuration.status = envVarsSet === requiredEnvVars.length ? '✅' : '⚠️';

    // 5. Check for common issues
    console.log('🔍 Checking for common issues...');

    // Check if there are any obvious syntax errors in key files
    const keyFiles = [
      'lib/supabase.js',
      'lib/auth-utils.js',
      'components/admin/AdminLayout.js'
    ];

    for (const filePath of keyFiles) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');

        // Basic syntax checks
        if (content.includes('import') && content.includes('export')) {
          results.configuration.details.push(`✅ ${filePath} has proper imports/exports`);
        }

        // Check for Supabase client initialization
        if (filePath.includes('supabase.js') && content.includes('createClient')) {
          results.configuration.details.push(`✅ Supabase client properly initialized`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Admin panel access test failed:', error.message);
    return false;
  }

  // Print Results
  console.log('\n📋 ADMIN PANEL ACCESS TEST RESULTS');
  console.log('===================================');
  console.log(`${results.pageFiles.status} Page Files:`);
  results.pageFiles.details.forEach(detail => console.log(`   • ${detail}`));

  console.log(`${results.components.status} Components:`);
  results.components.details.forEach(detail => console.log(`   • ${detail}`));

  console.log(`${results.apiEndpoints.status} API Endpoints:`);
  results.apiEndpoints.details.forEach(detail => console.log(`   • ${detail}`));

  console.log(`${results.configuration.status} Configuration:`);
  results.configuration.details.forEach(detail => console.log(`   • ${detail}`));

  const overallStatus = [
    results.pageFiles.status,
    results.components.status,
    results.apiEndpoints.status,
    results.configuration.status
  ].every(status => status === '✅') ? '✅ ADMIN PANEL READY' : '⚠️ ISSUES DETECTED';

  console.log(`\n🎯 Overall Admin Panel Status: ${overallStatus}`);

  return overallStatus.includes('READY');
}

// Run admin panel access tests
const scriptPath = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === scriptPath;

if (isMainModule) {
  testAdminPanelAccess()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Admin panel access test script failed:', error);
      process.exit(1);
    });
}

export { testAdminPanelAccess };
