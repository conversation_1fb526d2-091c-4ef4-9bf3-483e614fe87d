import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import BookingCalendar from '@/components/admin/BookingCalendar';
import BookingDetails from '@/components/admin/BookingDetails';
import EnhancedBookingDetails from '@/components/admin/EnhancedBookingDetails';
import BookingForm from '@/components/admin/BookingForm';
import BookingFilters from '@/components/admin/BookingFilters';
import BulkActionsToolbar from '@/components/admin/BulkActionsToolbar';
import Modal from '@/components/admin/Modal';
import supabase from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/BookingsPage.module.css';

export default function BookingsPage() {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(null); // 'create', 'edit', 'view'
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [bookingStats, setBookingStats] = useState({
    total: 0,
    confirmed: 0,
    pending: 0,
    canceled: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // New state for enhanced features
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [currentFilters, setCurrentFilters] = useState({});
  const [customers, setCustomers] = useState([]);
  const [services, setServices] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [isFiltering, setIsFiltering] = useState(false);

  // Fetch booking statistics
  useEffect(() => {
    const fetchBookingStats = async () => {
      // Don't fetch if auth is still loading or user is not authenticated
      if (authLoading || !isAuthenticated || !user) {
        console.log('Bookings: Waiting for authentication...');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Bookings: Fetching booking statistics...');

        // Get total bookings
        const { count: total, error: totalError } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true });

        if (totalError) throw totalError;

        // Get confirmed bookings
        const { count: confirmed, error: confirmedError } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'confirmed');

        if (confirmedError) throw confirmedError;

        // Get pending bookings
        const { count: pending, error: pendingError } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending');

        if (pendingError) throw pendingError;

        // Get canceled bookings
        const { count: canceled, error: canceledError } = await supabase
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'canceled');

        if (canceledError) throw canceledError;

        setBookingStats({
          total: total || 0,
          confirmed: confirmed || 0,
          pending: pending || 0,
          canceled: canceled || 0
        });

        console.log('Bookings: Statistics loaded successfully');
        setLoading(false);
      } catch (error) {
        console.error('Error fetching booking stats:', error);
        setError(error.message);
        setLoading(false);

        // Show user-friendly error message
        toast.error('Failed to load booking statistics. Please refresh the page.', {
          position: 'top-center',
          autoClose: 5000
        });
      }
    };

    fetchBookingStats();
  }, [refreshKey, isAuthenticated, user, authLoading]);

  // Handle booking selection
  const handleSelectBooking = (booking) => {
    setSelectedBooking(booking);
    setModalType('view');
    setShowModal(true);
  };

  // Handle slot selection for creating a new booking
  const handleSelectSlot = (slotInfo) => {
    setSelectedSlot(slotInfo);
    setModalType('create');
    setShowModal(true);
  };

  // Handle adding a new booking
  const handleAddBooking = () => {
    setSelectedBooking(null);
    setSelectedSlot(null);
    setModalType('create');
    setShowModal(true);
  };

  // Handle booking saved (created or updated)
  const handleBookingSaved = () => {
    // Refresh the calendar and stats
    setRefreshKey(prev => prev + 1);
    setShowModal(false);
    setSelectedBooking(null);
    setSelectedSlot(null);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Fetch customers and services for filters
  useEffect(() => {
    const fetchFilterData = async () => {
      if (!isAuthenticated || !user) return;

      try {
        // Fetch customers
        const { data: customersData, error: customersError } = await supabase
          .from('customers')
          .select('id, name, email')
          .order('name');

        if (customersError) throw customersError;

        // Fetch services
        const { data: servicesData, error: servicesError } = await supabase
          .from('services')
          .select('id, name, color')
          .order('name');

        if (servicesError) throw servicesError;

        setCustomers(customersData || []);
        setServices(servicesData || []);
      } catch (error) {
        console.error('Error fetching filter data:', error);
      }
    };

    fetchFilterData();
  }, [isAuthenticated, user]);

  // Handle filter changes
  const handleFiltersChange = async (filters) => {
    setCurrentFilters(filters);
    setIsFiltering(true);

    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters.service && filters.service !== 'all') params.append('service', filters.service);
      if (filters.customer && filters.customer !== 'all') params.append('customer', filters.customer);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.location) params.append('location', filters.location);

      // Fetch filtered bookings
      const response = await fetch(`/api/admin/bookings/search?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch filtered bookings');
      }

      setFilteredBookings(data.bookings || []);
    } catch (error) {
      console.error('Error filtering bookings:', error);
      toast.error('Failed to apply filters');
      setFilteredBookings([]);
    } finally {
      setIsFiltering(false);
    }
  };

  // Handle bulk operations
  const handleBulkAction = async (action, bookingIds) => {
    try {
      const response = await fetch('/api/admin/bookings/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          bookingIds: bookingIds.map(b => b.id),
          options: {
            sendNotifications: true
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Bulk operation failed');
      }

      // Handle export action
      if (action === 'export' && data.csvData) {
        const blob = new Blob([data.csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = data.filename || 'bookings_export.csv';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      // Show results
      if (data.success.length > 0) {
        toast.success(`Successfully processed ${data.success.length} booking(s)`);
      }

      if (data.failed.length > 0) {
        toast.warn(`Failed to process ${data.failed.length} booking(s)`);
      }

      // Refresh data
      setRefreshKey(prev => prev + 1);

      // Re-apply filters if active
      if (Object.keys(currentFilters).length > 0) {
        handleFiltersChange(currentFilters);
      }

    } catch (error) {
      console.error('Bulk operation error:', error);
      toast.error(`Failed to ${action} bookings: ${error.message}`);
    }
  };

  // Handle booking selection for bulk operations
  const handleBookingSelection = (booking, isSelected) => {
    setSelectedBookings(prev => {
      if (isSelected) {
        return [...prev, booking];
      } else {
        return prev.filter(b => b.id !== booking.id);
      }
    });
  };

  // Clear bulk selection
  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Booking Management">
        <div className={styles.bookingsPage}>
          {/* Show loading state while authentication is loading */}
          {authLoading && (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading authentication...</p>
            </div>
          )}

          {/* Show error state if there's an error */}
          {error && !authLoading && (
            <div className={styles.errorContainer}>
              <p className={styles.errorMessage}>Error: {error}</p>
              <button
                className={styles.retryButton}
                onClick={() => setRefreshKey(prev => prev + 1)}
              >
                Retry
              </button>
            </div>
          )}

          {/* Show main content when authenticated and no errors */}
          {!authLoading && !error && isAuthenticated && (
            <>
              <div className={styles.statsContainer}>
                <div className={styles.statCard}>
                  <div className={styles.statIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </div>
                  <div className={styles.statContent}>
                    <h3>Total Bookings</h3>
                    <p className={styles.statValue}>{loading ? '...' : bookingStats.total}</p>
                  </div>
                </div>

                <div className={styles.statCard}>
                  <div className={`${styles.statIcon} ${styles.confirmed}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div className={styles.statContent}>
                    <h3>Confirmed</h3>
                    <p className={styles.statValue}>{loading ? '...' : bookingStats.confirmed}</p>
                  </div>
                </div>

                <div className={styles.statCard}>
                  <div className={`${styles.statIcon} ${styles.pending}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <div className={styles.statContent}>
                    <h3>Pending</h3>
                    <p className={styles.statValue}>{loading ? '...' : bookingStats.pending}</p>
                  </div>
                </div>

                <div className={styles.statCard}>
                  <div className={`${styles.statIcon} ${styles.canceled}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="15" y1="9" x2="9" y2="15"></line>
                      <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                  </div>
                  <div className={styles.statContent}>
                    <h3>Canceled</h3>
                    <p className={styles.statValue}>{loading ? '...' : bookingStats.canceled}</p>
                  </div>
                </div>
              </div>

              {/* Enhanced Booking Filters */}
              <BookingFilters
                onFiltersChange={handleFiltersChange}
                customers={customers}
                services={services}
                initialFilters={currentFilters}
              />

              {/* Bulk Actions Toolbar */}
              {selectedBookings.length > 0 && (
                <BulkActionsToolbar
                  selectedBookings={selectedBookings}
                  onBulkAction={handleBulkAction}
                  onClearSelection={handleClearSelection}
                  disabled={isFiltering}
                />
              )}

              <div className={styles.actionBar}>
                <h2 className={styles.sectionTitle}>Booking Calendar</h2>
                <div className={styles.actionButtons}>
                  {isFiltering && (
                    <div className={styles.filteringIndicator}>
                      <div className={styles.spinner}></div>
                      <span>Applying filters...</span>
                    </div>
                  )}
                  <button
                    className={styles.addButton}
                    onClick={handleAddBooking}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Add Booking
                  </button>
                </div>
              </div>

              <div className={styles.contentContainer}>
                <div className={styles.calendarContainer}>
                  <BookingCalendar
                    key={refreshKey}
                    onSelectBooking={handleSelectBooking}
                    onSelectSlot={handleSelectSlot}
                    refreshKey={refreshKey}
                    filteredBookings={filteredBookings}
                    onBookingSelection={handleBookingSelection}
                    selectedBookings={selectedBookings}
                    showSelectionMode={selectedBookings.length > 0}
                  />
                </div>
              </div>

              {/* Modal for booking form */}
              {showModal && (
                <Modal onClose={handleCloseModal}>
                  {modalType === 'create' && (
                    <BookingForm
                      booking={null}
                      initialSlot={selectedSlot}
                      onSave={handleBookingSaved}
                      onCancel={handleCloseModal}
                    />
                  )}
                  {modalType === 'edit' && (
                    <BookingForm
                      booking={selectedBooking}
                      onSave={handleBookingSaved}
                      onCancel={handleCloseModal}
                    />
                  )}
                  {modalType === 'view' && (
                    <EnhancedBookingDetails
                      booking={selectedBooking}
                      onClose={handleCloseModal}
                      onEdit={() => setModalType('edit')}
                      onUpdate={handleBookingSaved}
                    />
                  )}
                </Modal>
              )}
            </>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
