import Head from 'next/head'
import Link from 'next/link'
import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import styles from '@/styles/Shop.module.css'
import Layout from '@/components/Layout'
// import OceanSparklesShowcase from '@/components/OceanSparklesShowcase' // Removed
import ProductCards from '@/components/ProductCards'
import SustainabilityShowcase from '@/components/SustainabilityShowcase'
import GiftCardShowcase from '@/components/GiftCardShowcase'
import { supabase } from '@/lib/supabase'

export default function Shop() {
  const [isAdmin, setIsAdmin] = useState(false);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Fetch products from database
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/public/products');

        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }

        const data = await response.json();
        setProducts(data.products || []);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err.message);
        // Fallback to hardcoded data if fetch fails
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          // Check if user has admin role
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('role')
            .eq('id', session.user.id)
            .single();

          setIsAdmin(profile?.role === 'admin');
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      }
    };

    checkAdminStatus();
  }, []);

  const handleEditProduct = (productId) => {
    router.push(`/admin/inventory?tab=products&edit=${productId}`);
  };

  // Fallback hardcoded product data (for when database is empty or fails)
  const additionalProducts = [
    {
      id: 'split-cake-cosmic',
      name: 'Split Cake - Cosmic Tide', // Updated Name
      description: 'Highly pigmented water-activated split cake palette with cosmic-inspired colors. Perfect for face and body art with seamless blending and bold coverage.',
      price: 40.00,
      image: '/images/products/splitcake-cosmic-pak.jpg', // Main image
      additionalImage: '/images/products/splitcake-cosmic-prod.jpg', // Quick view image
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival' // 'isNewArrival' converted to badge
    },
    {
      id: 'split-cake-ocean',
      name: 'Split Cake - Ocean Sounds', // Updated Name
      description: 'Water-activated split cake palette with ocean-inspired blues and teals. Creates stunning ocean-themed designs with highly pigmented colors that blend seamlessly.',
      price: 40.00,
      image: '/images/products/splitcake-ocean-pak.jpg',
      additionalImage: '/images/products/splitcake-ocean-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-neutral',
      name: 'Split Cake - Neutral Colour', // Updated Name
      description: 'Water-activated split cake palette with neutral earth tones. Perfect for creating natural looks, animal designs, and subtle face painting with highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-neutral-pak.jpg',
      additionalImage: '/images/products/splitcake-neutral-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-fire',
      name: 'Split Cake - Flaming Tiger', // Updated Name
      description: 'Water-activated split cake palette with fiery reds, oranges and yellows. Create dramatic flame effects and bold designs with these highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-flaimingtiger-pak.jpg',
      additionalImage: '/images/products/splitcake-flaimingtiger-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-pearl',
      name: 'Split Cake - Pearlescent Rose', // Updated Name
      description: 'Water-activated split cake palette with pearlescent rose tones. Creates elegant, shimmering designs with a beautiful pearlescent finish and highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-pearl-pak.jpg',
      additionalImage: '/images/products/splitcake-pearl-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-tropical',
      name: 'Split Cake - Tropical Paradise', // Updated Name
      description: 'Water-activated split cake palette with vibrant tropical colors. Perfect for creating exotic flowers, tropical designs, and festival looks with highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-tropical-pak.jpg',
      additionalImage: '/images/products/splitcake-tropical-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-galaxy',
      name: 'Split Cake - Galaxy Dreams', // Updated Name
      description: 'Water-activated split cake palette with cosmic galaxy colors. Create stunning space-themed designs with these highly pigmented colors that blend seamlessly.',
      price: 40.00,
      image: '/images/products/splitcake-galaxy-pak.jpg',
      additionalImage: '/images/products/splitcake-galaxy-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-aurora',
      name: 'Split Cake - Aurora Borealis', // Updated Name
      description: 'Water-activated split cake palette with aurora borealis-inspired colors. Create magical northern lights effects with these highly pigmented colors that blend beautifully.',
      price: 40.00,
      image: '/images/products/splitcake-aurora-pak.jpg',
      additionalImage: '/images/products/splitcake-aurora-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-forest',
      name: 'Split Cake - Forest Fairy', // Updated Name
      description: 'Water-activated split cake palette with forest-inspired greens. Perfect for creating woodland creatures, fairy designs, and nature-themed art with highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-forest-pak.jpg',
      additionalImage: '/images/products/splitcake-forest-prod.jpg',
      category: 'split-cakes',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'Blendable Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-rainbow-uv',
      name: 'Split Cake - Rainbow UV GLOW', // Name matches live site
      description: 'Water-activated split cake palette with UV-reactive rainbow colors. Create vibrant designs that glow under blacklight with these highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-rainbowuv-pak.jpg',
      additionalImage: '/images/products/splitcake-rainbowuv-prod.jpg',
      category: 'uv-products',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'UV-Reactive Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'split-cake-pastel-uv',
      name: 'Split Cake - Pastel UV GLOW', // Name matches live site
      description: 'Water-activated split cake palette with UV-reactive pastel colors. Create soft, ethereal designs that glow under blacklight with these highly pigmented colors.',
      price: 40.00,
      image: '/images/products/splitcake-pasteluv-pak.jpg',
      additionalImage: '/images/products/splitcake-pasteluv-prod.jpg',
      category: 'uv-products',
      features: [
        'Smudge-proof & Transfer-resistant',
        'Strong Pigment Shades',
        'UV-Reactive Formula',
        'Skin Safe',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Product Volume: 60g split cake',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    },
    {
      id: 'uv-liner-palette',
      name: 'UV LINER Water Activated Pallette', // Name matches live site (Pallette typo is on live site)
      description: 'Water-activated UV liner palette with 6 neon blacklight/UV glow shades and 2 classic shades. Perfect for creating bold eyeliner looks, intricate body art, and creative makeup designs.',
      price: 55.00,
      image: '/images/products/splitcake-uvliner-water activated-pak.jpg',
      additionalImage: '', // No prod image found
      category: 'uv-products',
      features: [
        'Smudge-proof & Transfer-resistant',
        '6 UV-Reactive Shades',
        '2 Classic Shades (Black & White)',
        'High-Quality Anti-rust Aluminum Pans',
        'Skin Safe & UV Reactive',
        'Cruelty-Free & Vegan-Friendly'
      ],
      details: [
        'Net Weight: 40g (8 x 5g each)',
        'Total Weight: 120g',
        'Water-Based Formula',
        'Not waterproof'
      ],
      badge: 'New Arrival'
    }
  ];

  // Use fetched products or fallback to hardcoded data
  const allProducts = products.length > 0 ? products : additionalProducts;

  // Updated Category data
  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 'split-cakes', name: 'Split Cakes' },
    { id: 'uv-products', name: 'UV Products' }
  ];

  // Featured products for hero section
  const featuredProducts = [
    {
      id: 'split-cake-cosmic',
      name: 'Split Cake - Cosmic Tide',
      image: '/images/products/splitcake-cosmic-pak.jpg',
      color: '#4ECDC4'
    },
    {
      id: 'split-cake-ocean',
      name: 'Split Cake - Ocean Sounds',
      image: '/images/products/splitcake-ocean-pak.jpg',
      color: '#FF6B6B'
    },
    {
      id: 'split-cake-pearl',
      name: 'Split Cake - Pearlescent Rose',
      image: '/images/products/splitcake-pearl-pak.jpg',
      color: '#FFE66D'
    },
    {
      id: 'split-cake-galaxy',
      name: 'Split Cake - Galaxy Dreams',
      image: '/images/products/splitcake-galaxy-pak.jpg',
      color: '#1A73E8'
    }
  ];

  // Sustainability benefits
  const sustainabilityBenefits = [
    {
      icon: '🌱',
      title: '100% Biodegradable',
      description: 'Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water.'
    },
    {
      icon: '🌊',
      title: 'Ocean Safe',
      description: 'Ensuring it leaves no harmful microplastics behind to damage marine life or ecosystems.'
    },
    {
      icon: '🌍',
      title: 'Sustainable Sourcing',
      description: 'Made from plant cellulose derived from responsibly grown eucalyptus trees.'
    },
    {
      icon: '🐰',
      title: 'Cruelty-Free',
      description: 'All our products are vegan-friendly and never tested on animals.'
    }
  ];

  // Gift card options
  const giftCardOptions = [
    { value: '25', label: '$25' },
    { value: '50', label: '$50' },
    { value: '75', label: '$75' },
    { value: '100', label: '$100' },
    { value: '200', label: '$200' }
  ];

  // Show loading state
  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Shop | OceanSoulSparkles</title>
          <meta name="description" content="Shop our range of eco-friendly glitter, face painting kits, split cakes, UV products, and accessories. All products are cruelty-free and environmentally friendly." />
        </Head>
        <main className={styles.main}>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading our amazing products...</p>
          </div>
        </main>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Shop | OceanSoulSparkles</title>
        <meta name="description" content="Shop our range of eco-friendly glitter, face painting kits, split cakes, UV products, and accessories. All products are cruelty-free and environmentally friendly." />
      </Head>

      <main className={styles.main}>
        {/* OceanSparklesShowcase removed */}

        {/* Product Cards - title and subtitle props removed */}
        <ProductCards
          products={allProducts} // Use the merged list
          categories={categories} // Use the updated categories
          id="products-collection" // Added ID for the CTA link
          isAdmin={isAdmin}
          onEditProduct={handleEditProduct}
        />

        {/* Sustainability Showcase */}
        <SustainabilityShowcase
          title="Our Commitment to Sustainability"
          image="/images/products/biodegradable-glitter.jpg"
          description="At OceanSoulSparkles, we believe in creating magic without harming the planet. All our glitter products are 100% biodegradable, made from plant cellulose derived from eucalyptus trees, and are completely vegan-friendly."
          benefits={sustainabilityBenefits}
        />

        {/* Gift Card Showcase */}
        <GiftCardShowcase
          title="Gift the Magic of Sparkles"
          subtitle="Perfect for birthdays, holidays, or just because - let them choose their own sparkle journey"
          giftCardOptions={giftCardOptions}
        />

        {/* Shipping Info Section */}
        <section className={styles.shippingInfo}>
          <h2>Shipping & Returns</h2>
          <div className={styles.infoGrid}>
            <div className={styles.infoCard}>
              <h3>Shipping</h3>
              <p>
                We ship Australia-wide with standard and express options available. Orders over $50 qualify for free standard shipping.
              </p>
              <ul>
                <li>Standard Parcel Post: Delivery within 2-6 business days ($10.95)</li>
                <li>Express Post: Next business day delivery within Express Post network ($14.95)</li>
                <li>International shipping options available</li>
              </ul>
              <Link href="/policies#shipping-info" className={styles.policyLink}>
                View Full Shipping Policy →
              </Link>
            </div>
            <div className={styles.infoCard}>
              <h3>Returns & Refunds</h3>
              <p>
                Due to the nature of cosmetics and hygiene concerns, we do not accept returns or exchanges on opened or used UV liner palettes and other cosmetic products.
              </p>
              <p>
                If your product arrives damaged or defective, please contact us within 7 days of receiving your package.
              </p>
              <Link href="/policies#return-policy" className={styles.policyLink}>
                View Full Return & Refund Policy →
              </Link>
            </div>
          </div>
        </section>

        {/* Custom Orders Section */}
        <section className={styles.customOrders}>
          <h2>Custom Orders</h2>
          <p>
            Looking for something specific? We offer custom orders for events, parties, or special occasions.
            Whether you need custom colors, bulk quantities, or personalized kits, we're happy to help.
          </p>
          <Link href="/contact" className={styles.button}>
            Contact Us for Custom Orders
          </Link>
        </section>

        {/* Admin Panel Link */}
        {isAdmin && (
          <div className={styles.adminPanelLink}>
            <Link href="/admin/inventory?tab=products" className={styles.adminButton}>
              ⚙️ Manage Products
            </Link>
          </div>
        )}
      </main>
    </Layout>
  )
}
