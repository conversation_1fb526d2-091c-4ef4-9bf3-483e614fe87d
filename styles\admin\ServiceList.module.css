/* ServiceList.module.css */
.serviceListContainer {
  background-color: #fff;
}

.filters {
  margin-bottom: 1.5rem;
}

.searchBox {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filterControls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterItem label {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: #666;
}

.filterItem select,
.filterItem input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.loadingSpinner {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.errorMessage {
  background-color: #fff0f0;
  color: #d32f2f;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  border-left: 4px solid #d32f2f;
}

.serviceList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.serviceItem {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 1rem 0;
  transition: background-color 0.2s;
  cursor: pointer;
}

.serviceItem:hover {
  background-color: #f9f9f9;
}

.serviceItem.selected {
  background-color: #f0f7ff;
}

.serviceImageContainer {
  width: 80px;
  height: 80px;
  position: relative;
  margin-right: 1rem;
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.serviceImage {
  object-fit: cover;
}

.servicePlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  color: #999;
  font-size: 1.5rem;
}

.serviceDetails {
  flex-grow: 1;
}

.serviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.serviceName {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.servicePrice {
  font-weight: 600;
  color: #2e7d32;
  font-size: 1rem;
}

.serviceCategory {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1565c0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.serviceStatus {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.statusActive {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusDraft {
  background-color: #fffde7;
  color: #f57f17;
}

.statusInactive {
  background-color: #f5f5f5;
  color: #757575;
}

.serviceDescription {
  color: #666;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.serviceFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #757575;
}

.serviceActions {
  display: flex;
  gap: 10px;
}

.actionButton {
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  color: #1976d2;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

.actionButton:hover {
  text-decoration: underline;
}

.noResults {
  text-align: center;
  padding: 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 4px;
}
