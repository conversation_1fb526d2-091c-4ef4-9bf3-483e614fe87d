/**
 * Direct Authentication Fix Script
 * 
 * This script directly fixes authentication issues by:
 * 1. Retrieving the current session from Supabase
 * 2. Storing the token in sessionStorage in the correct format
 * 3. Patching the fetch function to include the token in all admin API requests
 * 
 * To use this script:
 * 1. Open the browser console (F12 or right-click > Inspect > Console)
 * 2. Copy and paste the entire script into the console
 * 3. Press Enter to run the script
 * 4. The script will automatically fix the authentication issues
 */

(async function() {
  console.log('🔧 Starting direct authentication fix...');
  
  // Step 1: Clear all stored tokens
  console.log('🧹 Clearing all stored tokens...');
  
  try {
    // Clear sessionStorage
    if (window.sessionStorage) {
      sessionStorage.removeItem('oss_auth_token_cache');
      sessionStorage.removeItem('oss_session');
    }
    
    // Clear localStorage
    if (window.localStorage) {
      localStorage.removeItem('oss_auth_token');
      localStorage.removeItem('sb_auth_token');
    }
    
    // Clear cookies
    document.cookie = 'oss_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'sb_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    
    console.log('✅ All tokens cleared successfully');
  } catch (e) {
    console.error('❌ Error clearing tokens:', e);
  }
  
  // Step 2: Get the current session directly from Supabase
  console.log('🔍 Checking current session...');
  
  try {
    // Get the session from the API
    const response = await fetch('/api/admin/diagnostics/session-check');
    const data = await response.json();
    
    if (data.error) {
      console.error('❌ No active session found:', data.error);
      console.log('🔄 Redirecting to login page...');
      window.location.href = '/admin/login';
      return;
    }
    
    if (!data.session || !data.session.access_token) {
      console.error('❌ No valid token in session');
      console.log('🔄 Redirecting to login page...');
      window.location.href = '/admin/login';
      return;
    }
    
    console.log('✅ Active session found for:', data.session.user.email);
    
    // Step 3: Store the token in sessionStorage
    const token = data.session.access_token;
    
    // Parse the token to get expiry time
    let expiry = Date.now() + (60 * 60 * 1000); // Default: 1 hour from now
    
    try {
      const tokenParts = token.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1]));
        if (payload.exp) {
          expiry = payload.exp * 1000; // Convert to milliseconds
        }
      }
    } catch (e) {
      console.warn('⚠️ Could not parse token payload:', e);
    }
    
    // Store token in sessionStorage
    sessionStorage.setItem('oss_auth_token_cache', JSON.stringify({
      token: token,
      expiry: expiry,
      refreshed: Date.now()
    }));
    
    console.log('✅ Token stored in sessionStorage');
    console.log('📅 Token expires at:', new Date(expiry).toLocaleString());
    
    // Step 4: Patch the fetch function to include the token in all admin API requests
    if (!window._originalFetch) {
      window._originalFetch = window.fetch;
    }
    
    window.fetch = function patchedFetch(url, options = {}) {
      // Only intercept admin API requests
      if (typeof url === 'string' && (
          url.startsWith('/api/admin/') ||
          url.startsWith('/api/auth/') ||
          url.includes('requiresAuth=true')
      )) {
        // Clone options to avoid modifying the original
        const newOptions = { ...options };
        
        // Ensure headers object exists
        newOptions.headers = newOptions.headers || {};
        
        // Add the token to the Authorization header
        newOptions.headers['Authorization'] = `Bearer ${token}`;
        
        console.log(`🔒 Adding token to request: ${url}`);
        
        return window._originalFetch(url, newOptions);
      }
      
      // Use original fetch for other requests
      return window._originalFetch(url, options);
    };
    
    console.log('✅ Fetch function patched to include token in admin API requests');
    console.log('🎉 Authentication fix complete!');
    console.log('🔄 Reloading page to apply changes...');
    
    // Reload the page to apply changes
    window.location.reload();
  } catch (error) {
    console.error('❌ Authentication fix failed:', error);
    console.log('⚠️ Please try logging out and logging back in.');
  }
})();
