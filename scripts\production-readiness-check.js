/**
 * Ocean Soul Sparkles Production Readiness Validation System
 *
 * This script performs comprehensive validation of all systems before production deployment.
 * It tests API connections, authentication flows, webpage functionality, and integrations.
 *
 * Usage:
 * node scripts/production-readiness-check.js [--environment=production] [--verbose] [--fix-issues]
 */

import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  environment: process.argv.includes('--environment=production') ? 'production' : 'development',
  verbose: process.argv.includes('--verbose'),
  fixIssues: process.argv.includes('--fix-issues'),
  timeout: 10000, // 10 seconds timeout for requests
  maxRetries: 3
};

// Test results storage
const testResults = {
  timestamp: new Date().toISOString(),
  environment: config.environment,
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  },
  categories: {
    apiConnections: { tests: [], status: 'pending' },
    authentication: { tests: [], status: 'pending' },
    webpages: { tests: [], status: 'pending' },
    integrations: { tests: [], status: 'pending' },
    configuration: { tests: [], status: 'pending' }
  }
};

// Utility functions
const log = (message, level = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warning' ? '⚠️' : level === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);

  if (config.verbose || level === 'error') {
    console.log(message);
  }
};

const makeRequest = async (url, options = {}) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

const addTestResult = (category, testName, status, message, details = {}) => {
  const test = {
    name: testName,
    status,
    message,
    details,
    timestamp: new Date().toISOString()
  };

  testResults.categories[category].tests.push(test);
  testResults.summary.total++;

  if (status === 'passed') {
    testResults.summary.passed++;
    log(`✅ ${testName}: ${message}`, 'success');
  } else if (status === 'failed') {
    testResults.summary.failed++;
    log(`❌ ${testName}: ${message}`, 'error');
  } else if (status === 'warning') {
    testResults.summary.warnings++;
    log(`⚠️ ${testName}: ${message}`, 'warning');
  }
};

// Get authentication token for testing
const getAuthToken = async () => {
  try {
    // Try to read token from file if it exists
    const tokenFile = path.join(__dirname, '../.auth-token');
    if (fs.existsSync(tokenFile)) {
      return fs.readFileSync(tokenFile, 'utf8').trim();
    }

    // For production readiness, we need a valid token
    if (config.environment === 'production') {
      throw new Error('No authentication token found. Please provide a valid admin token for production testing.');
    }

    // For development, try to get a token via the dev auth endpoint
    const response = await makeRequest(`${config.baseUrl}/api/admin/diagnostics/dev-auth`);
    if (response.ok) {
      const data = await response.json();
      return data.token;
    }

    return null;
  } catch (error) {
    log(`Failed to get authentication token: ${error.message}`, 'error');
    return null;
  }
};

// Test category: API Connections
const testApiConnections = async (authToken) => {
  log('🔍 Testing API Connections...', 'info');

  // Test Supabase database connection
  try {
    const response = await makeRequest(`${config.baseUrl}/api/admin/diagnostics/database`, {
      headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
    });

    if (response.ok) {
      const data = await response.json();
      addTestResult('apiConnections', 'Supabase Database Connection', 'passed',
        'Database connection successful', { response: data });
    } else {
      addTestResult('apiConnections', 'Supabase Database Connection', 'failed',
        `Database connection failed: ${response.statusText}`);
    }
  } catch (error) {
    addTestResult('apiConnections', 'Supabase Database Connection', 'failed',
      `Database connection error: ${error.message}`);
  }

  // Test storage service
  try {
    const response = await makeRequest(`${config.baseUrl}/api/admin/diagnostics/storage`, {
      headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
    });

    if (response.ok) {
      const data = await response.json();
      addTestResult('apiConnections', 'Storage Service', 'passed',
        'Storage service accessible', { buckets: data.details?.buckets?.length || 0 });
    } else {
      addTestResult('apiConnections', 'Storage Service', 'failed',
        `Storage service failed: ${response.statusText}`);
    }
  } catch (error) {
    addTestResult('apiConnections', 'Storage Service', 'failed',
      `Storage service error: ${error.message}`);
  }

  // Test critical API endpoints
  const criticalEndpoints = [
    { name: 'Admin Health Check', path: '/api/admin/health' },
    { name: 'Customers API', path: '/api/admin/customers' },
    { name: 'Bookings API', path: '/api/admin/bookings' },
    { name: 'Services API', path: '/api/admin/services' },
    { name: 'Public Services API', path: '/api/public/services' },
    { name: 'Public Products API', path: '/api/public/products' }
  ];

  for (const endpoint of criticalEndpoints) {
    try {
      const response = await makeRequest(`${config.baseUrl}${endpoint.path}`, {
        headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
      });

      if (response.ok) {
        addTestResult('apiConnections', endpoint.name, 'passed',
          `${endpoint.name} responding correctly`);
      } else if (response.status === 401 && endpoint.path.includes('/admin/')) {
        addTestResult('apiConnections', endpoint.name, 'warning',
          `${endpoint.name} requires authentication (expected for admin endpoints)`);
      } else {
        addTestResult('apiConnections', endpoint.name, 'failed',
          `${endpoint.name} failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('apiConnections', endpoint.name, 'failed',
        `${endpoint.name} error: ${error.message}`);
    }
  }

  testResults.categories.apiConnections.status =
    testResults.categories.apiConnections.tests.every(t => t.status === 'passed') ? 'passed' : 'failed';
};

// Test category: Authentication System
const testAuthentication = async (authToken) => {
  log('🔐 Testing Authentication System...', 'info');

  // Test admin authentication
  if (authToken) {
    try {
      const response = await makeRequest(`${config.baseUrl}/api/admin/diagnostics/auth-check`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });

      if (response.ok) {
        const data = await response.json();
        addTestResult('authentication', 'Admin Authentication', 'passed',
          'Admin authentication working correctly', { user: data.user?.email });
      } else {
        addTestResult('authentication', 'Admin Authentication', 'failed',
          `Admin authentication failed: ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('authentication', 'Admin Authentication', 'failed',
        `Admin authentication error: ${error.message}`);
    }

    // Test role-based access control
    try {
      const response = await makeRequest(`${config.baseUrl}/api/admin/users`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });

      if (response.ok) {
        addTestResult('authentication', 'Role-Based Access Control', 'passed',
          'Admin role access working correctly');
      } else if (response.status === 403) {
        addTestResult('authentication', 'Role-Based Access Control', 'failed',
          'Admin role access denied - check user permissions');
      } else {
        addTestResult('authentication', 'Role-Based Access Control', 'warning',
          `RBAC test inconclusive: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('authentication', 'Role-Based Access Control', 'failed',
        `RBAC test error: ${error.message}`);
    }
  } else {
    addTestResult('authentication', 'Admin Authentication', 'failed',
      'No authentication token available for testing');
  }

  // Test JWT token validation
  try {
    const response = await makeRequest(`${config.baseUrl}/api/admin/diagnostics/auth-test`, {
      headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
    });

    if (response.ok) {
      const data = await response.json();
      addTestResult('authentication', 'JWT Token Validation', 'passed',
        'JWT token validation working', { tokenValid: data.authorized });
    } else {
      addTestResult('authentication', 'JWT Token Validation', 'failed',
        `JWT validation failed: ${response.statusText}`);
    }
  } catch (error) {
    addTestResult('authentication', 'JWT Token Validation', 'failed',
      `JWT validation error: ${error.message}`);
  }

  // Test public access (should not require authentication)
  try {
    const response = await makeRequest(`${config.baseUrl}/api/public/services`);

    if (response.ok) {
      addTestResult('authentication', 'Public API Access', 'passed',
        'Public APIs accessible without authentication');
    } else {
      addTestResult('authentication', 'Public API Access', 'failed',
        `Public API access failed: ${response.statusText}`);
    }
  } catch (error) {
    addTestResult('authentication', 'Public API Access', 'failed',
      `Public API access error: ${error.message}`);
  }

  testResults.categories.authentication.status =
    testResults.categories.authentication.tests.every(t => t.status === 'passed') ? 'passed' : 'failed';
};

// Test category: Webpage Functionality
const testWebpages = async () => {
  log('🌐 Testing Webpage Functionality...', 'info');

  const publicPages = [
    { name: 'Home Page', path: '/' },
    { name: 'About Page', path: '/about' },
    { name: 'Services Page', path: '/services' },
    { name: 'Shop Page', path: '/shop' },
    { name: 'Contact Page', path: '/contact' },
    { name: 'Book Online Page', path: '/book-online' },
    { name: 'Gallery Page', path: '/gallery' }
  ];

  for (const page of publicPages) {
    try {
      const response = await makeRequest(`${config.baseUrl}${page.path}`);

      if (response.ok) {
        const html = await response.text();

        // Basic HTML validation
        if (html.includes('<html') && html.includes('</html>')) {
          addTestResult('webpages', page.name, 'passed',
            `${page.name} loads correctly`);
        } else {
          addTestResult('webpages', page.name, 'warning',
            `${page.name} loads but may have HTML issues`);
        }
      } else {
        addTestResult('webpages', page.name, 'failed',
          `${page.name} failed to load: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('webpages', page.name, 'failed',
        `${page.name} error: ${error.message}`);
    }
  }

  // Test admin pages (require authentication)
  const adminPages = [
    { name: 'Admin Dashboard', path: '/admin' },
    { name: 'Admin Customers', path: '/admin/customers' },
    { name: 'Admin Bookings', path: '/admin/bookings' },
    { name: 'Admin Inventory', path: '/admin/inventory' },
    { name: 'Admin Diagnostics', path: '/admin/diagnostics' }
  ];

  for (const page of adminPages) {
    try {
      const response = await makeRequest(`${config.baseUrl}${page.path}`);

      if (response.ok) {
        addTestResult('webpages', page.name, 'passed',
          `${page.name} accessible`);
      } else if (response.status === 401 || response.status === 403) {
        addTestResult('webpages', page.name, 'passed',
          `${page.name} properly protected (authentication required)`);
      } else {
        addTestResult('webpages', page.name, 'failed',
          `${page.name} failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult('webpages', page.name, 'failed',
        `${page.name} error: ${error.message}`);
    }
  }

  testResults.categories.webpages.status =
    testResults.categories.webpages.tests.every(t => t.status === 'passed') ? 'passed' : 'failed';
};

// Test category: Integrations
const testIntegrations = async () => {
  log('🔌 Testing Integrations...', 'info');

  // Test OneSignal integration
  try {
    const oneSignalAppId = process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID;
    if (oneSignalAppId) {
      addTestResult('integrations', 'OneSignal Configuration', 'passed',
        'OneSignal app ID configured', { appId: oneSignalAppId.substring(0, 8) + '...' });
    } else {
      addTestResult('integrations', 'OneSignal Configuration', 'warning',
        'OneSignal app ID not configured');
    }
  } catch (error) {
    addTestResult('integrations', 'OneSignal Configuration', 'failed',
      `OneSignal configuration error: ${error.message}`);
  }

  // Test email configuration
  try {
    const emailConfig = {
      host: process.env.EMAIL_HOST,
      user: process.env.EMAIL_USER,
      from: process.env.EMAIL_FROM
    };

    if (emailConfig.host && emailConfig.user) {
      addTestResult('integrations', 'Email Configuration', 'passed',
        'Email configuration present');
    } else {
      addTestResult('integrations', 'Email Configuration', 'warning',
        'Email configuration incomplete or missing');
    }
  } catch (error) {
    addTestResult('integrations', 'Email Configuration', 'failed',
      `Email configuration error: ${error.message}`);
  }

  // Test payment integrations (if configured)
  try {
    const squareConfig = {
      appId: process.env.SQUARE_APPLICATION_ID,
      locationId: process.env.SQUARE_LOCATION_ID
    };

    if (squareConfig.appId && squareConfig.locationId) {
      addTestResult('integrations', 'Square Payment Integration', 'passed',
        'Square payment configuration present');
    } else {
      addTestResult('integrations', 'Square Payment Integration', 'warning',
        'Square payment configuration not found');
    }
  } catch (error) {
    addTestResult('integrations', 'Square Payment Integration', 'failed',
      `Square configuration error: ${error.message}`);
  }

  testResults.categories.integrations.status =
    testResults.categories.integrations.tests.every(t => t.status === 'passed') ? 'passed' : 'failed';
};

// Test category: Configuration
const testConfiguration = async () => {
  log('⚙️ Testing Configuration...', 'info');

  // Test environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_SITE_URL'
  ];

  const missingEnvVars = [];
  const presentEnvVars = [];

  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      presentEnvVars.push(envVar);
    } else {
      missingEnvVars.push(envVar);
    }
  }

  if (missingEnvVars.length === 0) {
    addTestResult('configuration', 'Required Environment Variables', 'passed',
      'All required environment variables present', { count: presentEnvVars.length });
  } else {
    addTestResult('configuration', 'Required Environment Variables', 'failed',
      `Missing environment variables: ${missingEnvVars.join(', ')}`);
  }

  // Test production-specific configuration
  if (config.environment === 'production') {
    const productionEnvVars = [
      'NEXT_PUBLIC_SECURE_COOKIES',
      'NEXT_PUBLIC_COOKIE_DOMAIN'
    ];

    const missingProdVars = productionEnvVars.filter(envVar => !process.env[envVar]);

    if (missingProdVars.length === 0) {
      addTestResult('configuration', 'Production Environment Variables', 'passed',
        'Production environment variables configured');
    } else {
      addTestResult('configuration', 'Production Environment Variables', 'warning',
        `Missing production variables: ${missingProdVars.join(', ')}`);
    }

    // Check for development mode in production
    if (process.env.NEXT_PUBLIC_DEV_MODE === 'true') {
      addTestResult('configuration', 'Development Mode Check', 'failed',
        'Development mode is enabled in production environment');
    } else {
      addTestResult('configuration', 'Development Mode Check', 'passed',
        'Development mode properly disabled for production');
    }
  }

  // Test build configuration
  try {
    const packageJsonPath = path.join(__dirname, '../package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

      if (packageJson.scripts && packageJson.scripts.build && packageJson.scripts.start) {
        addTestResult('configuration', 'Build Scripts', 'passed',
          'Build and start scripts configured');
      } else {
        addTestResult('configuration', 'Build Scripts', 'failed',
          'Missing required build or start scripts');
      }
    } else {
      addTestResult('configuration', 'Build Scripts', 'failed',
        'package.json not found');
    }
  } catch (error) {
    addTestResult('configuration', 'Build Scripts', 'failed',
      `Build configuration error: ${error.message}`);
  }

  testResults.categories.configuration.status =
    testResults.categories.configuration.tests.every(t => t.status === 'passed') ? 'passed' : 'failed';
};

// Generate comprehensive report
const generateReport = () => {
  const reportPath = path.join(__dirname, '../production-readiness-report.json');

  // Calculate overall status
  const overallStatus = Object.values(testResults.categories).every(cat => cat.status === 'passed')
    ? 'READY' : 'NOT_READY';

  const report = {
    ...testResults,
    overallStatus,
    recommendations: generateRecommendations()
  };

  // Save detailed JSON report
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  // Generate console summary
  console.log('\n' + '='.repeat(80));
  console.log('🌊 OCEAN SOUL SPARKLES PRODUCTION READINESS REPORT 🌊');
  console.log('='.repeat(80));
  console.log(`\n📊 OVERALL STATUS: ${overallStatus === 'READY' ? '✅ READY FOR PRODUCTION' : '❌ NOT READY FOR PRODUCTION'}`);
  console.log(`📅 Test Date: ${testResults.timestamp}`);
  console.log(`🌍 Environment: ${config.environment}`);

  console.log('\n📈 SUMMARY:');
  console.log(`   Total Tests: ${testResults.summary.total}`);
  console.log(`   ✅ Passed: ${testResults.summary.passed}`);
  console.log(`   ❌ Failed: ${testResults.summary.failed}`);
  console.log(`   ⚠️ Warnings: ${testResults.summary.warnings}`);

  console.log('\n🔍 CATEGORY BREAKDOWN:');
  Object.entries(testResults.categories).forEach(([category, data]) => {
    const statusIcon = data.status === 'passed' ? '✅' : '❌';
    const testCount = data.tests.length;
    const passedCount = data.tests.filter(t => t.status === 'passed').length;
    console.log(`   ${statusIcon} ${category.toUpperCase()}: ${passedCount}/${testCount} tests passed`);
  });

  // Show critical failures
  const criticalFailures = [];
  Object.entries(testResults.categories).forEach(([category, data]) => {
    data.tests.forEach(test => {
      if (test.status === 'failed') {
        criticalFailures.push(`${category}: ${test.name} - ${test.message}`);
      }
    });
  });

  if (criticalFailures.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES TO RESOLVE:');
    criticalFailures.forEach((failure, index) => {
      console.log(`   ${index + 1}. ${failure}`);
    });
  }

  // Show recommendations
  const recommendations = generateRecommendations();
  if (recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }

  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  console.log('='.repeat(80));

  return overallStatus === 'READY';
};

// Generate recommendations based on test results
const generateRecommendations = () => {
  const recommendations = [];

  // Check for authentication issues
  const authTests = testResults.categories.authentication.tests;
  if (authTests.some(t => t.status === 'failed')) {
    recommendations.push('Fix authentication system issues before deployment');
  }

  // Check for API connection issues
  const apiTests = testResults.categories.apiConnections.tests;
  if (apiTests.some(t => t.status === 'failed')) {
    recommendations.push('Resolve API connection issues - check Supabase configuration');
  }

  // Check for missing environment variables
  const configTests = testResults.categories.configuration.tests;
  const envTest = configTests.find(t => t.name === 'Required Environment Variables');
  if (envTest && envTest.status === 'failed') {
    recommendations.push('Configure all required environment variables');
  }

  // Production-specific recommendations
  if (config.environment === 'production') {
    const prodEnvTest = configTests.find(t => t.name === 'Production Environment Variables');
    if (prodEnvTest && prodEnvTest.status === 'warning') {
      recommendations.push('Configure production-specific environment variables for optimal security');
    }

    const devModeTest = configTests.find(t => t.name === 'Development Mode Check');
    if (devModeTest && devModeTest.status === 'failed') {
      recommendations.push('Disable development mode for production deployment');
    }
  }

  // Integration recommendations
  const integrationTests = testResults.categories.integrations.tests;
  if (integrationTests.some(t => t.status === 'warning')) {
    recommendations.push('Consider configuring optional integrations (email, payments, notifications) for full functionality');
  }

  return recommendations;
};

// Main execution function
const main = async () => {
  console.log('🌊 Ocean Soul Sparkles Production Readiness Validation System 🌊');
  console.log(`Environment: ${config.environment}`);
  console.log(`Base URL: ${config.baseUrl}`);
  console.log('Starting comprehensive validation...\n');

  try {
    // Get authentication token
    const authToken = await getAuthToken();
    if (authToken) {
      log('Authentication token obtained successfully', 'success');
    } else {
      log('No authentication token available - some tests may be limited', 'warning');
    }

    // Run all test categories
    await testConfiguration();
    await testApiConnections(authToken);
    await testAuthentication(authToken);
    await testWebpages();
    await testIntegrations();

    // Generate and display report
    const isReady = generateReport();

    // Exit with appropriate code
    process.exit(isReady ? 0 : 1);

  } catch (error) {
    log(`Fatal error during validation: ${error.message}`, 'error');
    console.error(error);
    process.exit(1);
  }
};

// Run the validation if this script is executed directly
const scriptPath = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === scriptPath;

if (isMainModule) {
  main();
}
