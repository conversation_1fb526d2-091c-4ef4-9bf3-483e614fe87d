import { useEffect, useRef } from 'react';
import styles from '@/styles/QuickViewModal.module.css';

/**
 * QuickViewModal component for displaying product details in a modal
 * 
 * @param {Object} props - Component props
 * @param {Object} props.product - Product object to display
 * @param {Function} props.onClose - Function to call when modal is closed
 * @param {Function} props.onAddToCart - Function to call when add to cart button is clicked
 * @returns {JSX.Element}
 */
const QuickViewModal = ({ product, onClose, onAddToCart }) => {
  const modalRef = useRef(null);
  
  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent scrolling when modal is open
    document.body.style.overflow = 'hidden';
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);
  
  // Close modal when ESC key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscKey);
    
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);
  
  if (!product) return null;
  
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal} ref={modalRef}>
        <button className={styles.closeButton} onClick={onClose}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        <div className={styles.modalContent}>
          <div className={styles.productImage}>
            <img 
              src={product.additionalImage || product.image} 
              alt={product.name} 
            />
            {product.badge && (
              <span className={styles.productBadge}>{product.badge}</span>
            )}
          </div>
          
          <div className={styles.productDetails}>
            <h2 className={styles.productName}>{product.name}</h2>
            <p className={styles.productPrice}>${product.price?.toFixed(2)}</p>
            <p className={styles.productDescription}>{product.description}</p>
            
            {product.features && product.features.length > 0 && (
              <div className={styles.productFeatures}>
                <h3>Features</h3>
                <ul>
                  {product.features.map((feature, index) => (
                    <li key={index}>{feature}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {product.details && product.details.length > 0 && (
              <div className={styles.productDetails}>
                <h3>Details</h3>
                <ul>
                  {product.details.map((detail, index) => (
                    <li key={index}>{detail}</li>
                  ))}
                </ul>
              </div>
            )}
            
            <button 
              className={styles.addToCartButton}
              onClick={() => onAddToCart(product)}
            >
              Add to Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickViewModal;
