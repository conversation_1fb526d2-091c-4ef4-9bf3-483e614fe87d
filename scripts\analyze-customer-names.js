#!/usr/bin/env node

/**
 * Customer Name Analysis Script
 * 
 * This script analyzes customer names in the database to understand
 * naming patterns for fuzzy matching with invoice customer names.
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Invoice customer names from the CSV (first 20 for analysis)
const invoiceCustomerNames = [
  "Ashleigh  Noonan",
  "Frosty Solana Collective", 
  "Eat The Beat",
  "Housing First",
  "<PERSON>",
  "Kstar Group ATF",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Banyule City Council",
  "<PERSON>lar<PERSON>NS<PERSON>",
  "Antonella Face Painting Melbourne",
  "Techa",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>lle",
  "<PERSON>",
  "<PERSON>",
  "Housing First (Ruby)",
  "Nelson Alexander",
  "Ivanhoe East Primary School",
  "<EMAIL>",
  "Riely Saville",
  "Amanda",
  "Kristy Kumar",
  "Kate - Virtue Vice",
  "La Trobe University",
  "Luke Priday",
  "Balloonaversal Entertainments Peter Patterson",
  "Officeworks Accounts Payable",
  "Patricia Bowlby"
];

async function analyzeCustomerNames() {
  console.log('🔍 Analyzing Customer Names for Fuzzy Matching...\n');
  
  try {
    // Get sample customer names from database
    const { data: customers, error } = await supabase
      .from('customers')
      .select('id, name, email')
      .limit(50);

    if (error) {
      console.error('❌ Error fetching customers:', error.message);
      return;
    }

    console.log('📊 DATABASE CUSTOMER NAMES (Sample):');
    console.log('=====================================');
    customers.slice(0, 20).forEach((customer, index) => {
      console.log(`${index + 1}. "${customer.name}" (${customer.email})`);
    });

    console.log('\n📋 INVOICE CUSTOMER NAMES:');
    console.log('===========================');
    invoiceCustomerNames.slice(0, 20).forEach((name, index) => {
      console.log(`${index + 1}. "${name}"`);
    });

    // Analyze naming patterns
    console.log('\n🔍 NAMING PATTERN ANALYSIS:');
    console.log('============================');
    
    const dbNames = customers.map(c => c.name);
    
    // Check for potential matches
    console.log('\n🎯 POTENTIAL MATCHES FOUND:');
    console.log('============================');
    
    let matchCount = 0;
    for (const invoiceName of invoiceCustomerNames) {
      const matches = findPotentialMatches(invoiceName, dbNames);
      if (matches.length > 0) {
        matchCount++;
        console.log(`\n"${invoiceName}" might match:`);
        matches.forEach(match => {
          console.log(`  → "${match.name}" (similarity: ${match.similarity.toFixed(2)})`);
        });
      }
    }

    console.log(`\n📈 MATCHING STATISTICS:`);
    console.log(`========================`);
    console.log(`Invoice names analyzed: ${invoiceCustomerNames.length}`);
    console.log(`Database names available: ${dbNames.length}`);
    console.log(`Potential matches found: ${matchCount}`);
    console.log(`Match rate: ${((matchCount / invoiceCustomerNames.length) * 100).toFixed(1)}%`);

    // Analyze naming patterns
    analyzeNamingPatterns(dbNames, invoiceCustomerNames);

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

function findPotentialMatches(invoiceName, dbNames, threshold = 0.6) {
  const matches = [];
  
  for (const dbName of dbNames) {
    const similarity = calculateSimilarity(invoiceName, dbName);
    if (similarity >= threshold) {
      matches.push({ name: dbName, similarity });
    }
  }
  
  return matches.sort((a, b) => b.similarity - a.similarity);
}

function calculateSimilarity(str1, str2) {
  // Simple Levenshtein distance-based similarity
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const distance = levenshteinDistance(longer.toLowerCase(), shorter.toLowerCase());
  return (longer.length - distance) / longer.length;
}

function levenshteinDistance(str1, str2) {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

function analyzeNamingPatterns(dbNames, invoiceNames) {
  console.log(`\n🔍 NAMING PATTERN INSIGHTS:`);
  console.log(`===========================`);
  
  // Analyze database names
  const dbPatterns = {
    hasFirstLast: 0,
    hasCompanyKeywords: 0,
    hasSpecialChars: 0,
    avgLength: 0
  };
  
  dbNames.forEach(name => {
    if (name.includes(' ') && name.split(' ').length >= 2) dbPatterns.hasFirstLast++;
    if (/\b(pty|ltd|group|council|university|school|entertainment)\b/i.test(name)) dbPatterns.hasCompanyKeywords++;
    if (/[()@-]/.test(name)) dbPatterns.hasSpecialChars++;
    dbPatterns.avgLength += name.length;
  });
  
  dbPatterns.avgLength = dbPatterns.avgLength / dbNames.length;
  
  // Analyze invoice names
  const invoicePatterns = {
    hasFirstLast: 0,
    hasCompanyKeywords: 0,
    hasSpecialChars: 0,
    avgLength: 0
  };
  
  invoiceNames.forEach(name => {
    if (name.includes(' ') && name.split(' ').length >= 2) invoicePatterns.hasFirstLast++;
    if (/\b(pty|ltd|group|council|university|school|entertainment)\b/i.test(name)) invoicePatterns.hasCompanyKeywords++;
    if (/[()@-]/.test(name)) invoicePatterns.hasSpecialChars++;
    invoicePatterns.avgLength += name.length;
  });
  
  invoicePatterns.avgLength = invoicePatterns.avgLength / invoiceNames.length;
  
  console.log(`Database Names:`);
  console.log(`  • First/Last format: ${((dbPatterns.hasFirstLast / dbNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Company keywords: ${((dbPatterns.hasCompanyKeywords / dbNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Special characters: ${((dbPatterns.hasSpecialChars / dbNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Average length: ${dbPatterns.avgLength.toFixed(1)} characters`);
  
  console.log(`\nInvoice Names:`);
  console.log(`  • First/Last format: ${((invoicePatterns.hasFirstLast / invoiceNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Company keywords: ${((invoicePatterns.hasCompanyKeywords / invoiceNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Special characters: ${((invoicePatterns.hasSpecialChars / invoiceNames.length) * 100).toFixed(1)}%`);
  console.log(`  • Average length: ${invoicePatterns.avgLength.toFixed(1)} characters`);
}

// Run analysis
if (require.main === module) {
  analyzeCustomerNames()
    .then(() => {
      console.log('\n✅ Analysis completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

module.exports = { analyzeCustomerNames };
