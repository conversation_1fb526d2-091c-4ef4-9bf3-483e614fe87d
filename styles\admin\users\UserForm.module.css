.userFormContainer {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formRow {
  display: flex;
  gap: 20px;
}

.formRow .formGroup {
  flex: 1;
}

.formGroup label {
  font-weight: 500;
  color: #444;
  font-size: 0.95rem;
}

.input,
.select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus {
  border-color: #6a0dad;
  outline: none;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

.inputError {
  border-color: #f44336;
}

.inputError:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
}

.errorMessage {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 4px;
}

.helpText {
  font-size: 0.85rem;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.passwordInputContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.passwordInputContainer .input {
  width: 100%;
  padding-right: 40px;
}

.passwordToggle {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.passwordToggle:hover {
  color: #495057;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #e9e9e9;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 15px;
  }
}
