/**
 * Login-specific Error Handling
 * 
 * This script provides specialized error handling for the login page
 * to prevent white screens during authentication.
 */

(function() {
  'use strict';

  // Configuration
  const DEBUG = true;
  const MAX_RETRY_COUNT = 3;
  const RETRY_DELAY = 1000; // 1 second
  
  // State management
  let retryCount = 0;
  let hasRendered = false;
  let loginFormVisible = false;
  
  // Wait for DOM to be loaded
  function onDOMReady(callback) {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', callback);
    } else {
      callback();
    }
  }
  
  // Check if login form is visible
  function checkLoginFormVisibility() {
    const loginForm = document.querySelector('form');
    const emailInput = document.querySelector('input[type="email"]');
    
    loginFormVisible = !!(loginForm && emailInput);
    
    if (DEBUG) {
      console.log('[Login Debug] Login form visible:', loginFormVisible);
    }
    
    return loginFormVisible;
  }
  
  // Monitor the login page for potential issues
  function monitorLoginPage() {
    hasRendered = document.body && document.body.children.length > 0;
    
    if (!hasRendered || !checkLoginFormVisibility()) {
      // Page hasn't fully rendered yet or login form is not visible
      if (retryCount < MAX_RETRY_COUNT) {
        retryCount++;
        
        if (DEBUG) {
          console.log(`[Login Debug] Attempt ${retryCount}/${MAX_RETRY_COUNT} to fix login page rendering`);
        }
        
        // Clear potentially corrupted auth state
        if (typeof localStorage !== 'undefined') {
          try {
            const authKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('supabase') || key.includes('auth'))) {
                authKeys.push(key);
              }
            }
            
            if (authKeys.length > 0) {
              console.log('[Login Debug] Clearing potentially corrupted auth data from localStorage:', authKeys);
              authKeys.forEach(key => localStorage.removeItem(key));
            }
          } catch (e) {
            console.error('[Login Debug] Error clearing localStorage:', e);
          }
        }
        
        // Retry after delay
        setTimeout(monitorLoginPage, RETRY_DELAY);
      } else {
        // Max retries reached, show emergency login UI
        showEmergencyLoginUI();
      }
    } else {
      // Login form is visible, page rendered successfully
      if (DEBUG) {
        console.log('[Login Debug] Login page rendered successfully');
      }
    }
  }
  
  // Show emergency login UI if login form fails to render
  function showEmergencyLoginUI() {
    if (document.getElementById('emergency-login')) {
      return; // Already created
    }
    
    console.warn('[Login Debug] Showing emergency login UI');
    
    const emergencyDiv = document.createElement('div');
    emergencyDiv.id = 'emergency-login';
    emergencyDiv.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f8f9fa;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      color: #333;
      padding: 20px;
    `;
    
    emergencyDiv.innerHTML = `
      <div style="max-width: 500px; text-align: center;">
        <h2 style="margin-bottom: 20px; color: #4a6bdf;">OceanSoulSparkles Admin</h2>
        <div style="background: white; border-radius: 8px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <h3 style="margin-top: 0;">Emergency Login</h3>
          <p style="margin-bottom: 20px;">The normal login page is having trouble loading. You can use this emergency form to log in:</p>
          
          <form id="emergency-login-form" style="text-align: left;">
            <div style="margin-bottom: 15px;">
              <label for="emergency-email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email</label>
              <input type="email" id="emergency-email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
            </div>
            
            <div style="margin-bottom: 20px;">
              <label for="emergency-password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password</label>
              <input type="password" id="emergency-password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
            </div>
            
            <button type="submit" style="width: 100%; padding: 12px; background: #4a6bdf; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">Log In</button>
          </form>
          
          <div id="emergency-login-error" style="color: #e74c3c; margin-top: 15px; display: none;"></div>
          
          <div style="margin-top: 20px; display: flex; justify-content: space-between;">
            <button id="reset-auth-storage" style="background: #f39c12; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer;">Reset Auth Storage</button>
            <button id="reload-page" style="background: #27ae60; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer;">Reload Page</button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(emergencyDiv);
    
    // Add event listeners
    document.getElementById('emergency-login-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const email = document.getElementById('emergency-email').value;
      const password = document.getElementById('emergency-password').value;
      const errorDiv = document.getElementById('emergency-login-error');
      
      // Basic validation
      if (!email || !password) {
        errorDiv.textContent = 'Email and password are required';
        errorDiv.style.display = 'block';
        return;
      }
      
      // Attempt login
      errorDiv.style.display = 'none';
      errorDiv.textContent = '';
      
      // Supabase should be available in the global scope
      if (window.supabase && window.supabase.auth) {
        window.supabase.auth.signInWithPassword({ email, password })
          .then(result => {
            if (result.error) {
              errorDiv.textContent = result.error.message || 'Authentication failed';
              errorDiv.style.display = 'block';
            } else {
              // Login successful
              window.location.href = '/admin';
            }
          })
          .catch(err => {
            errorDiv.textContent = err.message || 'An unexpected error occurred';
            errorDiv.style.display = 'block';
          });
      } else {
        errorDiv.textContent = 'Authentication system not available. Please try again later.';
        errorDiv.style.display = 'block';
      }
    });
    
    document.getElementById('reset-auth-storage').addEventListener('click', function() {
      // Clear all auth-related storage
      try {
        if (localStorage) {
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('supabase') || key.includes('auth'))) {
              localStorage.removeItem(key);
            }
          }
        }
        
        if (sessionStorage) {
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && (key.includes('supabase') || key.includes('auth'))) {
              sessionStorage.removeItem(key);
            }
          }
        }
        
        // Show confirmation
        const errorDiv = document.getElementById('emergency-login-error');
        errorDiv.textContent = 'Auth storage cleared successfully!';
        errorDiv.style.color = '#27ae60';
        errorDiv.style.display = 'block';
      } catch (e) {
        console.error('[Login Debug] Error clearing storage:', e);
      }
    });
    
    document.getElementById('reload-page').addEventListener('click', function() {
      window.location.reload(true); // Force reload from server
    });
  }
  
  // Start monitoring when DOM is ready
  onDOMReady(function() {
    // Only run on login page
    if (window.location.pathname.includes('/admin/login')) {
      console.log('[Login Debug] Login page detected, initializing monitoring');
      setTimeout(monitorLoginPage, 300); // Give a short delay for initial render
    }
  });
})();
