.settingsForm {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.formSection {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.formSection:last-child {
  border-bottom: none;
}

.formSection h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.formGroup {
  margin-bottom: 1.25rem;
  width: 100%;
}

.formRow {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.formRow .formGroup {
  flex: 1;
  min-width: 250px;
}

.formGroup label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.formGroup input[type="text"],
.formGroup input[type="email"],
.formGroup input[type="tel"],
.formGroup input[type="number"],
.formGroup input[type="url"],
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input[type="text"]:focus,
.formGroup input[type="email"]:focus,
.formGroup input[type="tel"]:focus,
.formGroup input[type="number"]:focus,
.formGroup input[type="url"]:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.formGroup small {
  display: block;
  font-size: 0.75rem;
  color: #718096;
  margin-top: 0.375rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
}

.colorPickerWrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.colorPickerWrapper input[type="color"] {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  cursor: pointer;
}

.colorPickerWrapper input[type="text"] {
  flex: 1;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
}

.saveButton {
  background-color: #3788d8;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover {
  background-color: #2c6cb0;
}

.saveButton:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}
