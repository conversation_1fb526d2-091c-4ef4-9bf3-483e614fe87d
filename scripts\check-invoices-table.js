#!/usr/bin/env node

/**
 * Check Invoices Table Structure
 * 
 * This script checks if the invoices table exists and what its structure is
 * to ensure the import script uses the correct column names.
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkInvoicesTable() {
  console.log('🔍 Checking Invoices Table Structure...\n');
  
  try {
    // Try to get table structure by attempting to select with limit 0
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .limit(0);

    if (error) {
      if (error.message.includes('relation "public.invoices" does not exist')) {
        console.log('❌ INVOICES TABLE DOES NOT EXIST');
        console.log('Need to create the invoices table first.');
        
        // Suggest table creation
        console.log('\n📝 SUGGESTED TABLE CREATION SQL:');
        console.log('================================');
        console.log(`
CREATE TABLE public.invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number TEXT NOT NULL UNIQUE,
  customer_id UUID REFERENCES public.customers(id) NOT NULL,
  order_id UUID REFERENCES public.orders(id),
  booking_id UUID REFERENCES public.bookings(id),
  amount DECIMAL(10, 2) NOT NULL,
  subtotal DECIMAL(10, 2),
  discount DECIMAL(10, 2) DEFAULT 0,
  tax_amount DECIMAL(10, 2) DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'AUD',
  issue_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL,
  order_number TEXT,
  customer_name_original TEXT,
  match_type TEXT,
  match_confidence DECIMAL(3,2),
  source TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_invoices_customer_id ON public.invoices(customer_id);
CREATE INDEX idx_invoices_invoice_number ON public.invoices(invoice_number);
CREATE INDEX idx_invoices_status ON public.invoices(status);
CREATE INDEX idx_invoices_issue_date ON public.invoices(issue_date);
        `);
        
        return false;
      } else {
        console.error('❌ Error checking invoices table:', error.message);
        return false;
      }
    }

    console.log('✅ INVOICES TABLE EXISTS');
    
    // Try to insert a test record to see what columns are available
    const testRecord = {
      invoice_number: 'TEST_' + Date.now(),
      customer_id: '00000000-0000-0000-0000-000000000000', // This will fail but show us the structure
      amount: 100.00,
      currency: 'AUD',
      issue_date: new Date().toISOString(),
      due_date: new Date().toISOString(),
      status: 'draft'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('invoices')
      .insert([testRecord])
      .select();

    if (insertError) {
      console.log('📋 TABLE STRUCTURE ANALYSIS FROM ERROR:');
      console.log('======================================');
      console.log('Error:', insertError.message);
      
      // Parse error to understand structure
      if (insertError.message.includes('violates foreign key constraint')) {
        console.log('✅ Foreign key constraints are working');
      }
      
      if (insertError.message.includes('column') && insertError.message.includes('does not exist')) {
        console.log('❌ Some columns may not exist in the table');
      }
    } else {
      console.log('✅ Test insert successful (this should not happen with fake customer_id)');
      
      // Clean up test record
      if (insertData && insertData[0]) {
        await supabase
          .from('invoices')
          .delete()
          .eq('id', insertData[0].id);
      }
    }

    // Try to get existing invoices to see current structure
    const { data: existingInvoices, error: selectError } = await supabase
      .from('invoices')
      .select('*')
      .limit(1);

    if (!selectError && existingInvoices) {
      console.log('\n📊 EXISTING INVOICES COUNT:');
      console.log('===========================');
      
      const { count, error: countError } = await supabase
        .from('invoices')
        .select('*', { count: 'exact', head: true });

      if (!countError) {
        console.log(`Current invoices in database: ${count}`);
      }

      if (existingInvoices.length > 0) {
        console.log('\n📋 SAMPLE INVOICE STRUCTURE:');
        console.log('============================');
        const sample = existingInvoices[0];
        Object.keys(sample).forEach(key => {
          console.log(`${key}: ${typeof sample[key]} (${sample[key]})`);
        });
      }
    }

    return true;

  } catch (error) {
    console.error('❌ Failed to check invoices table:', error.message);
    return false;
  }
}

// Run check
if (require.main === module) {
  checkInvoicesTable()
    .then(success => {
      if (success) {
        console.log('\n✅ Invoices table check completed');
      } else {
        console.log('\n❌ Invoices table check failed');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Check script failed:', error);
      process.exit(1);
    });
}

module.exports = { checkInvoicesTable };
