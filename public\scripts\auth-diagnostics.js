/**
 * Authentication Diagnostics Script
 * 
 * This script provides comprehensive authentication debugging information
 * Run in browser console: authDiagnostics.run()
 */

window.authDiagnostics = {
  /**
   * Run comprehensive authentication diagnostics
   */
  async run() {
    console.log('🔍 Authentication Diagnostics Starting...');
    console.log('==========================================');
    
    // 1. Check environment
    this.checkEnvironment();
    
    // 2. Check storage
    this.checkStorage();
    
    // 3. Check network
    await this.checkNetwork();
    
    // 4. Check Supabase
    await this.checkSupabase();
    
    // 5. Test auth endpoints
    await this.testAuthEndpoints();
    
    console.log('==========================================');
    console.log('🔍 Authentication Diagnostics Complete');
  },

  /**
   * Check browser environment
   */
  checkEnvironment() {
    console.log('\n📱 Browser Environment:');
    console.log(`URL: ${window.location.href}`);
    console.log(`User Agent: ${navigator.userAgent}`);
    console.log(`Cookies Enabled: ${navigator.cookieEnabled}`);
    console.log(`Local Storage Available: ${typeof(Storage) !== "undefined"}`);
    console.log(`Session Storage Available: ${typeof(sessionStorage) !== "undefined"}`);
  },

  /**
   * Check local storage and session storage
   */
  checkStorage() {
    console.log('\n💾 Storage Contents:');
    
    // Local Storage
    console.log('Local Storage:');
    const localStorageKeys = ['oss_auth_token', 'sb_auth_token', 'supabase.auth.token'];
    localStorageKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        console.log(`  ${key}: ${value.substring(0, 50)}...`);
      } else {
        console.log(`  ${key}: null`);
      }
    });

    // Session Storage
    console.log('Session Storage:');
    const sessionKeys = Object.keys(sessionStorage);
    if (sessionKeys.length > 0) {
      sessionKeys.forEach(key => {
        const value = sessionStorage.getItem(key);
        console.log(`  ${key}: ${value ? value.substring(0, 50) + '...' : 'null'}`);
      });
    } else {
      console.log('  (empty)');
    }

    // Cookies
    console.log('Cookies:');
    if (document.cookie) {
      const cookies = document.cookie.split(';');
      cookies.forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name.includes('auth') || name.includes('token')) {
          console.log(`  ${name}: ${value ? value.substring(0, 50) + '...' : 'null'}`);
        }
      });
    } else {
      console.log('  (no cookies)');
    }
  },

  /**
   * Check network connectivity
   */
  async checkNetwork() {
    console.log('\n🌐 Network Connectivity:');
    
    try {
      const response = await fetch('/api/health', { method: 'GET' });
      console.log(`Health Check: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.error('Health Check Failed:', error.message);
    }
  },

  /**
   * Check Supabase connection
   */
  async checkSupabase() {
    console.log('\n🔑 Supabase Connection:');
    
    try {
      // Check if Supabase is available
      if (typeof window.supabase === 'undefined') {
        console.error('Supabase client not available globally');
        return;
      }

      // Test basic connection
      const { data, error } = await window.supabase.auth.getSession();
      
      if (error) {
        console.error('Supabase Session Error:', error);
      } else {
        console.log(`Session Status: ${data.session ? 'Active' : 'No Session'}`);
        if (data.session) {
          console.log(`User ID: ${data.session.user?.id}`);
          console.log(`User Email: ${data.session.user?.email}`);
          console.log(`Expires At: ${new Date(data.session.expires_at * 1000)}`);
        }
      }
    } catch (error) {
      console.error('Supabase Check Failed:', error);
    }
  },

  /**
   * Test authentication endpoints
   */
  async testAuthEndpoints() {
    console.log('\n🔌 Auth Endpoint Tests:');
    
    const endpoints = [
      '/api/admin/health',
      '/api/auth/test',
      '/api/admin/diagnostics/auth-check'
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing ${endpoint}...`);
        
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('oss_auth_token') || localStorage.getItem('sb_auth_token') || ''}`,
            'X-Auth-Token': localStorage.getItem('oss_auth_token') || localStorage.getItem('sb_auth_token') || '',
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();
        console.log(`  ${endpoint}: ${response.status} ${response.statusText}`);
        console.log(`  Response:`, data);
        
      } catch (error) {
        console.error(`  ${endpoint}: Failed -`, error.message);
      }
    }
  },

  /**
   * Clear all authentication data
   */
  clearAuth() {
    console.log('🧹 Clearing all authentication data...');
    
    // Clear localStorage
    const authKeys = ['oss_auth_token', 'sb_auth_token', 'supabase.auth.token'];
    authKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Removed: ${key}`);
    });

    // Clear sessionStorage
    sessionStorage.clear();
    console.log('Session storage cleared');

    // Clear Supabase session if available
    if (window.supabase) {
      window.supabase.auth.signOut();
      console.log('Supabase session cleared');
    }

    console.log('✅ Authentication data cleared. Reload the page to test.');
  },

  /**
   * Quick recovery attempt
   */
  async quickRecovery() {
    console.log('🚀 Attempting quick recovery...');
    
    try {
      // Clear existing auth data
      this.clearAuth();
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to login
      window.location.href = '/admin/login';
      
    } catch (error) {
      console.error('Quick recovery failed:', error);
    }
  }
};

// Auto-run diagnostics if in admin area and having issues
if (window.location.pathname.startsWith('/admin') && document.readyState === 'complete') {
  // Check for common auth issues
  const hasAuthToken = localStorage.getItem('oss_auth_token') || localStorage.getItem('sb_auth_token');
  const isLoginPage = window.location.pathname.includes('/login');
  
  if (!hasAuthToken && !isLoginPage) {
    console.warn('⚠️ No auth token found on admin page. Run authDiagnostics.run() for details.');
  }
}

console.log('🔍 Auth Diagnostics loaded. Use: authDiagnostics.run()');
