import { useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from '@/styles/ServicesHeroShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * ServicesHeroShowcase component with visual effects and animated content
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Hero section title
 * @param {string} props.subtitle - Hero section subtitle
 * @param {string} props.backgroundImage - URL of the background image
 * @param {Array} props.services - Array of service highlight objects
 * @param {string} props.ctaText - Call to action button text
 * @param {string} props.ctaLink - Call to action button link
 * @returns {JSX.Element}
 */
const ServicesHeroShowcase = ({
  title = 'Our Services',
  subtitle = 'Transforming moments into memories with magic and creativity',
  backgroundImage = '/background.png',
  services = [],
  ctaText = 'Book Now',
  ctaLink = '/book-online',
  ...props
}) => {
  const showcaseRef = useRef(null);
  const floatingCirclesRef = useRef(null);

  useEffect(() => {
    // Create floating animation for circles
    const handleMouseMove = (e) => {
      if (!floatingCirclesRef.current || !showcaseRef.current) return;

      const { clientX, clientY } = e;
      const { left, top, width, height } = showcaseRef.current.getBoundingClientRect();

      // Calculate mouse position relative to the container
      const x = (clientX - left) / width - 0.5;
      const y = (clientY - top) / height - 0.5;

      // Apply parallax effect to floating circles
      floatingCirclesRef.current.style.transform = `translate(${x * 30}px, ${y * 30}px)`;
    };

    // Parallax effect on scroll - matching the home hero implementation
    const handleScroll = () => {
      if (!showcaseRef.current) return;

      const scrollPosition = window.scrollY;
      const parallaxOffset = scrollPosition * 0.4; // Same speed as home hero

      // Apply parallax effect to background - using calc for smoother effect
      showcaseRef.current.style.backgroundPositionY = `calc(50% + ${parallaxOffset}px)`;
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Default services if none provided
  const defaultServices = [
    {
      title: 'Face Painting',
      image: '/images/services/face-painting.jpg',
      icon: '🎨',
      color: '#4ECDC4'
    },
    {
      title: 'Airbrush Body Art',
      image: '/Airbrush Face Body Painting.png',
      icon: '✨',
      color: '#FF6B6B'
    },
    {
      title: 'Festival Braiding',
      image: '/images/services/braiding.jpg',
      icon: '💇',
      color: '#FFE66D'
    },
    {
      title: 'Glitter Designs',
      image: '/Glitter Bar.png',
      icon: '⭐',
      color: '#1A73E8'
    }
  ];

  const showcaseServices = services.length > 0 ? services : defaultServices;

  return (
    <section
      ref={showcaseRef}
      className={styles.servicesShowcase}
      style={{ backgroundImage: `url(${backgroundImage})` }}
      {...props}
    >
      <div className={styles.overlayGradient}></div>
      <div ref={floatingCirclesRef} className={styles.floatingCircles}>
        {showcaseServices.map((service, index) => (
          <div
            key={index}
            className={styles.floatingCircle}
            style={{
              backgroundColor: 'transparent',
              animationDelay: `${index * 0.3}s`
            }}
          >
            <div className={styles.circleImageContainer}>
              {service.image ? (
                <Image
                  src={service.image}
                  alt={service.title}
                  width={150}
                  height={150}
                  className={styles.circleImage}
                />
              ) : (
                <span className={styles.circleIcon}>{service.icon}</span>
              )}
            </div>
            <div className={styles.circleOverlay} style={{ backgroundColor: service.color }}></div>
          </div>
        ))}
      </div>

      <div className={styles.showcaseContent}>
        <AnimatedSection animation="fade-in" delay={100}>
          <h1 className={styles.showcaseTitle}>{title}</h1>
          <p className={styles.showcaseSubtitle}>{subtitle}</p>

          <div className={styles.showcaseCta}>
            <Link href={ctaLink} className="button button--glow">
              {ctaText}
            </Link>
          </div>
        </AnimatedSection>
      </div>

      <div className={styles.scrollIndicator}>
        <div className={styles.mouseIcon}></div>
        <span>Explore our services</span>
      </div>
    </section>
  );
};

export default ServicesHeroShowcase;