import { useEffect, useRef } from 'react';
import styles from '@/styles/SustainabilityShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * SustainabilityShowcase component to highlight eco-friendly products
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {string} props.image - Featured image
 * @param {string} props.description - Description text about sustainability
 * @param {Array} props.benefits - Array of eco benefits
 * @returns {JSX.Element}
 */
const SustainabilityShowcase = ({
  title = 'Our Commitment to Sustainability',
  image = '/images/products/biodegradable-glitter.jpg',
  description = 'At OceanSoulSparkles, we believe in creating magic without harming the planet. All our glitter products are 100% biodegradable, made from plant cellulose derived from eucalyptus trees, and are completely vegan-friendly.',
  benefits = [],
  ...props
}) => {
  const showcaseRef = useRef(null);
  const imageRef = useRef(null);
  
  // Default benefits if none provided
  const defaultBenefits = [
    {
      icon: '🌱',
      title: '100% Biodegradable',
      description: 'Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water.'
    },
    {
      icon: '🌊',
      title: 'Ocean Safe',
      description: 'Ensuring it leaves no harmful microplastics behind to damage marine life or ecosystems.'
    },
    {
      icon: '🌍',
      title: 'Sustainable Sourcing',
      description: 'Made from plant cellulose derived from responsibly grown eucalyptus trees.'
    },
    {
      icon: '🐰',
      title: 'Cruelty-Free',
      description: 'All our products are vegan-friendly and never tested on animals.'
    }
  ];
  
  const displayBenefits = benefits.length > 0 ? benefits : defaultBenefits;
  
  useEffect(() => {
    // Parallax effect on scroll
    const handleScroll = () => {
      if (!showcaseRef.current || !imageRef.current) return;
      
      const scrollPosition = window.scrollY;
      const showcasePosition = showcaseRef.current.getBoundingClientRect().top + scrollPosition;
      const offset = scrollPosition - showcasePosition;
      
      if (offset >= 0) {
        // Parallax effect for image
        imageRef.current.style.transform = `translateY(${offset * 0.2}px)`;
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  return (
    <section className={styles.sustainabilityShowcase} ref={showcaseRef} {...props}>
      <div className={styles.waveTop}>
        <svg viewBox="0 0 1440 120" xmlns="http://www.w3.org/2000/svg">
          <path d="M0,64L80,58.7C160,53,320,43,480,53.3C640,64,800,96,960,101.3C1120,107,1280,85,1360,74.7L1440,64L1440,0L1360,0C1280,0,1120,0,960,0C800,0,640,0,480,0C320,0,160,0,80,0L0,0Z" fill="white"></path>
        </svg>
      </div>
      
      <div className={styles.showcaseContent}>
        <div className={styles.textContent}>
          <AnimatedSection animation="fade-right">
            <h2 className={styles.showcaseTitle}>{title}</h2>
            <p className={styles.showcaseDescription}>{description}</p>
            
            <div className={styles.benefitsGrid}>
              {displayBenefits.map((benefit, index) => (
                <AnimatedSection
                  key={index}
                  animation="fade-up"
                  delay={index * 150}
                  className={styles.benefitCard}
                >
                  <div className={styles.benefitIcon}>{benefit.icon}</div>
                  <h3 className={styles.benefitTitle}>{benefit.title}</h3>
                  <p className={styles.benefitDescription}>{benefit.description}</p>
                </AnimatedSection>
              ))}
            </div>
          </AnimatedSection>
        </div>
        
        <div className={styles.imageContent}>
          <div className={styles.imageContainer}>
            <div className={styles.floatingElements}>
              <div className={styles.floatingLeaf} style={{ top: '10%', left: '15%', animationDelay: '0s' }}>🍃</div>
              <div className={styles.floatingLeaf} style={{ top: '70%', left: '20%', animationDelay: '1.5s' }}>🌿</div>
              <div className={styles.floatingLeaf} style={{ top: '30%', right: '20%', animationDelay: '0.7s' }}>🌱</div>
              <div className={styles.floatingLeaf} style={{ top: '80%', right: '25%', animationDelay: '2.2s' }}>🍃</div>
            </div>
            <div className={styles.imageBorder}>
              <div ref={imageRef} className={styles.imageWrapper}>
                <img src={image} alt="Eco-friendly product" className={styles.showcaseImage} />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className={styles.waveBottom}>
        <svg viewBox="0 0 1440 120" xmlns="http://www.w3.org/2000/svg">
          <path d="M0,64L80,58.7C160,53,320,43,480,53.3C640,64,800,96,960,101.3C1120,107,1280,85,1360,74.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z" fill="white"></path>
        </svg>
      </div>
    </section>
  );
};

export default SustainabilityShowcase; 