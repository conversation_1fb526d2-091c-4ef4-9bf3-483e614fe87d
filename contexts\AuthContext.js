import { createContext, useContext, useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/router'
import supabase, { getCurrentUser } from '../lib/supabase'
import { toast } from 'react-toastify'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  // Maintain existing state structure
  // Maintain patched method references
  const [user, setUser] = useState(null)
  const [role, setRole] = useState(null)
  const authInstanceRef = useRef(null);
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const router = useRouter()

  // Admin route detection
  const isAdminRoute = () => {
    return router.pathname.startsWith('/admin')
  }

  // Public admin paths that don't require authentication
  const publicAdminPaths = [
    '/admin/login',
    '/admin/reset-password',
    '/admin/forgot-password'
  ]

  // Check if current path requires authentication
  const isProtectedAdminRoute = () => {
    return isAdminRoute() && !publicAdminPaths.some(path => router.pathname.includes(path))
  }

  // Redirect to appropriate login page
  const redirectToLogin = () => {
    if (typeof window !== 'undefined') {
      const currentPath = encodeURIComponent(router.asPath)
      if (isAdminRoute()) {
        sessionStorage.setItem('redirect_after_login', router.pathname)
        router.push('/admin/login')
      } else {
        router.push(`/login?redirect=${currentPath}`)
      }
    }
  }

  // Initialize auth state using Supabase directly
  useEffect(() => {
    let authListener
    let mounted = true
    const authId = Math.random().toString(36).substring(2, 8)

    // Skip re-initialization if we already have a user and we're just navigating
    if (user && !loading && !error) {
      console.log(`[Auth ${authId}] User already authenticated, skipping re-initialization`)
      return
    }

    // Prevent multiple simultaneous initializations
    if (authInstanceRef.current) {
      console.log(`[Auth ${authId}] Auth initialization already in progress, skipping`)
      return
    }
    authInstanceRef.current = authId

    async function initializeAuth() {
      try {
        console.log(`[Auth ${authId}] Initializing auth context`)

        // Clear any stale authentication data first
        if (typeof window !== 'undefined') {
          try {
            // Clear potentially corrupted cached data
            const cachedAuth = sessionStorage.getItem('oss_auth_state')
            if (cachedAuth) {
              const { user: cachedUser, role: cachedRole, timestamp } = JSON.parse(cachedAuth)
              // Only use cached data if it's less than 2 minutes old (reduced from 5 minutes)
              if (cachedUser && Date.now() - timestamp < 2 * 60 * 1000) {
                console.log(`[Auth ${authId}] Using cached auth state`)
                setUser(cachedUser)
                setRole(cachedRole)
                setLoading(false)
                // Still verify with server in background
              } else {
                console.log(`[Auth ${authId}] Cached auth state expired, clearing`)
                sessionStorage.removeItem('oss_auth_state')
              }
            }
          } catch (cacheError) {
            console.warn(`[Auth ${authId}] Error reading cached auth state, clearing:`, cacheError)
            sessionStorage.removeItem('oss_auth_state')
          }
        }

        // Get initial session and user data
        try {
          console.log(`[Auth ${authId}] Getting current user`)
          const { user: currentUser, role: userRole } = await getCurrentUser()

          if (currentUser && mounted) {
            console.log(`[Auth ${authId}] Setting user and role`)
            setUser(currentUser)
            setRole(userRole)

            // Cache auth state for faster navigation
            if (typeof window !== 'undefined') {
              try {
                const authState = {
                  user: currentUser,
                  role: userRole,
                  timestamp: Date.now()
                }
                sessionStorage.setItem('oss_auth_state', JSON.stringify(authState))
                console.log(`[Auth ${authId}] Cached auth state`)
              } catch (cacheError) {
                console.warn(`[Auth ${authId}] Error caching auth state:`, cacheError)
              }
            }

            // For admin routes, verify admin/staff role
            if (isProtectedAdminRoute() && !['admin', 'staff'].includes(userRole)) {
              console.warn(`[Auth ${authId}] User lacks admin privileges for admin route`)
              toast.error('Access denied. Admin privileges required.', {
                autoClose: 5000,
                position: 'top-center'
              })
              redirectToLogin()
              return
            }
          } else {
            console.log(`[Auth ${authId}] No active user session found`)

            // Redirect to login if on protected route
            if (isProtectedAdminRoute()) {
              console.log(`[Auth ${authId}] Redirecting to admin login`)
              redirectToLogin()
            }
          }
        } catch (getUserError) {
          console.error(`[Auth ${authId}] Error getting current user:`, getUserError)
          if (mounted) setError(getUserError)
        }

        // Subscribe to auth changes
        console.log(`[Auth ${authId}] Setting up auth subscription`)
        try {
          // Use Supabase's onAuthStateChange method
          const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
              if (!mounted) return

              console.log(`[Auth ${authId}] Auth event: ${event}`, session ? 'Session exists' : 'No session')

              if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
                if (session) {
                  try {
                    console.log(`[Auth ${authId}] Getting updated user after ${event}`)
                    const { user: updatedUser, role: updatedRole } = await getCurrentUser()
                    if (mounted) {
                      console.log(`[Auth ${authId}] Updating user and role`)
                      setUser(updatedUser)
                      setRole(updatedRole)
                      setError(null)

                      // Show success notification for sign in
                      if (event === 'SIGNED_IN') {
                        toast.success('Successfully signed in!', {
                          autoClose: 3000,
                          position: 'top-right'
                        })
                      }

                      // For admin routes, verify admin/staff role
                      if (isProtectedAdminRoute() && !['admin', 'staff'].includes(updatedRole)) {
                        console.warn(`[Auth ${authId}] User lacks admin privileges for admin route`)
                        toast.error('Access denied. Admin privileges required.', {
                          autoClose: 5000,
                          position: 'top-center'
                        })
                        redirectToLogin()
                        return
                      }
                    }
                  } catch (updateError) {
                    console.error(`[Auth ${authId}] Error updating user after ${event}:`, updateError)
                  }
                }
              } else if (event === 'SIGNED_OUT') {
                if (mounted) {
                  console.log(`[Auth ${authId}] Clearing user and role after sign out`)
                  setUser(null)
                  setRole(null)
                  setError(null)

                  // Show notification
                  toast.info('You have been signed out', {
                    autoClose: 3000,
                    position: 'top-right'
                  })

                  // Redirect to login if on protected route
                  if (isProtectedAdminRoute()) {
                    redirectToLogin()
                  }
                }
              } else if (event === 'USER_UPDATED') {
                if (session && mounted) {
                  try {
                    const { user: updatedUser, role: updatedRole } = await getCurrentUser()
                    setUser(updatedUser)
                    setRole(updatedRole)
                  } catch (updateError) {
                    console.error(`[Auth ${authId}] Error updating user after USER_UPDATED:`, updateError)
                  }
                }
              }
            }
          )

          authListener = subscription
          console.log(`[Auth ${authId}] Auth subscription set up successfully`)
        } catch (subscriptionError) {
          console.error(`[Auth ${authId}] Error setting up auth subscription:`, subscriptionError)
          if (mounted) setError(subscriptionError)
        }
      } catch (err) {
        console.error(`[Auth ${authId}] Failed to initialize auth:`, err)
        if (mounted) setError(err)
      } finally {
        // Always set loading to false to prevent UI from being stuck
        if (mounted) {
          console.log(`[Auth ${authId}] Auth initialization complete, setting loading to false`)
          setLoading(false)
        }
      }
    }

    // Start the initialization process
    initializeAuth()

    // Cleanup subscription on unmount
    return () => {
      console.log(`[Auth ${authId}] Cleaning up auth context`)
      mounted = false
      if (authListener) {
        try {
          console.log(`[Auth ${authId}] Unsubscribing from auth changes`)
          authListener.unsubscribe()
        } catch (cleanupError) {
          console.error(`[Auth ${authId}] Error during auth subscription cleanup:`, cleanupError)
        }
      }
      // Clear the auth instance reference
      if (authInstanceRef.current === authId) {
        authInstanceRef.current = null
      }
    }
  }, [router])

  // Sign in handler using Supabase directly
  const signIn = async (email, password) => {
    setLoading(true)
    setError(null)
    try {
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (signInError) throw signInError

      // Get user role after successful sign in
      const { user: currentUser, role: userRole } = await getCurrentUser()

      return {
        data: {
          ...data,
          user: currentUser,
          role: userRole
        },
        error: null
      }
    } catch (err) {
      setError(err)
      return { data: null, error: err }
    } finally {
      setLoading(false)
    }
  }

  // Sign out handler using Supabase directly
  const signOut = async () => {
    setLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      setUser(null)
      setRole(null)
      setError(null)

      // Clear cached auth state
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.removeItem('oss_auth_state')
          sessionStorage.removeItem('oss_auth_token_cache')
          console.log('Cleared cached auth state on sign out')
        } catch (cacheError) {
          console.warn('Error clearing cached auth state:', cacheError)
        }
      }
    } catch (err) {
      console.error('Sign out error:', err)
      setError(err)
    } finally {
      setLoading(false)
    }
  }

  const value = {
    user,
    role,
    loading,
    error,
    isAdmin: role === 'admin',
    isStaff: role === 'staff',
    isAuthenticated: !!user,
    signIn,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
