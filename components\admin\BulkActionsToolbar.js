import { useState } from 'react';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/BulkActionsToolbar.module.css';

export default function BulkActionsToolbar({ 
  selectedBookings = [], 
  onBulkAction, 
  onClearSelection,
  disabled = false
}) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const bulkActions = [
    { 
      value: 'confirm', 
      label: 'Confirm Selected', 
      icon: '✓',
      color: 'success',
      requiresConfirmation: true,
      description: 'Mark selected bookings as confirmed'
    },
    { 
      value: 'cancel', 
      label: 'Cancel Selected', 
      icon: '✕',
      color: 'danger',
      requiresConfirmation: true,
      description: 'Cancel selected bookings'
    },
    { 
      value: 'complete', 
      label: 'Mark Complete', 
      icon: '✅',
      color: 'success',
      requiresConfirmation: true,
      description: 'Mark selected bookings as completed'
    },
    { 
      value: 'reschedule', 
      label: 'Bulk Reschedule', 
      icon: '📅',
      color: 'primary',
      requiresConfirmation: false,
      description: 'Reschedule multiple bookings'
    },
    { 
      value: 'export', 
      label: 'Export Selected', 
      icon: '📊',
      color: 'secondary',
      requiresConfirmation: false,
      description: 'Export selected bookings to CSV'
    },
    { 
      value: 'send_reminder', 
      label: 'Send Reminders', 
      icon: '📧',
      color: 'primary',
      requiresConfirmation: false,
      description: 'Send reminder notifications to customers'
    },
    { 
      value: 'duplicate', 
      label: 'Duplicate', 
      icon: '📋',
      color: 'secondary',
      requiresConfirmation: false,
      description: 'Create copies of selected bookings'
    }
  ];

  const handleBulkAction = async (action) => {
    if (selectedBookings.length === 0) {
      toast.warn('Please select at least one booking');
      return;
    }

    const actionConfig = bulkActions.find(a => a.value === action);
    
    if (actionConfig?.requiresConfirmation) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      await executeBulkAction(action);
    }
  };

  const executeBulkAction = async (action) => {
    setIsProcessing(true);
    
    try {
      console.log(`Executing bulk action: ${action} on ${selectedBookings.length} bookings`);
      
      // Call the parent component's bulk action handler
      await onBulkAction(action, selectedBookings);
      
      // Show success message
      const actionConfig = bulkActions.find(a => a.value === action);
      toast.success(`Successfully ${actionConfig?.label.toLowerCase()} ${selectedBookings.length} booking(s)`);
      
      // Clear selection after successful action
      onClearSelection();
      
    } catch (error) {
      console.error('Bulk action error:', error);
      toast.error(`Failed to ${action} bookings: ${error.message}`);
    } finally {
      setIsProcessing(false);
      setShowConfirmation(false);
      setPendingAction(null);
    }
  };

  const confirmBulkAction = async () => {
    if (pendingAction) {
      await executeBulkAction(pendingAction);
    }
  };

  const cancelConfirmation = () => {
    setShowConfirmation(false);
    setPendingAction(null);
  };

  const getActionButtonClass = (action) => {
    const baseClass = styles.actionButton;
    const colorClass = styles[action.color] || styles.primary;
    return `${baseClass} ${colorClass}`;
  };

  const getConfirmationMessage = () => {
    const actionConfig = bulkActions.find(a => a.value === pendingAction);
    const actionName = actionConfig?.label.toLowerCase() || pendingAction;
    
    switch (pendingAction) {
      case 'cancel':
        return `Are you sure you want to cancel ${selectedBookings.length} booking(s)? This will notify customers and cannot be easily undone.`;
      case 'confirm':
        return `Are you sure you want to confirm ${selectedBookings.length} booking(s)? This will send confirmation notifications to customers.`;
      case 'complete':
        return `Are you sure you want to mark ${selectedBookings.length} booking(s) as completed? This action cannot be undone.`;
      default:
        return `Are you sure you want to ${actionName} ${selectedBookings.length} booking(s)?`;
    }
  };

  if (selectedBookings.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`${styles.toolbar} ${disabled ? styles.disabled : ''}`}>
        <div className={styles.selectionInfo}>
          <div className={styles.selectionCount}>
            <span className={styles.count}>
              {selectedBookings.length}
            </span>
            <span className={styles.countLabel}>
              booking{selectedBookings.length !== 1 ? 's' : ''} selected
            </span>
          </div>
          <button
            onClick={onClearSelection}
            className={styles.clearSelection}
            disabled={disabled || isProcessing}
          >
            Clear Selection
          </button>
        </div>

        <div className={styles.actions}>
          {bulkActions.map(action => (
            <button
              key={action.value}
              onClick={() => handleBulkAction(action.value)}
              className={getActionButtonClass(action)}
              disabled={disabled || isProcessing || selectedBookings.length === 0}
              title={action.description}
            >
              <span className={styles.actionIcon}>{action.icon}</span>
              <span className={styles.actionLabel}>{action.label}</span>
              {isProcessing && pendingAction === action.value && (
                <span className={styles.spinner}></span>
              )}
            </button>
          ))}
        </div>

        {/* Quick Stats */}
        <div className={styles.quickStats}>
          <div className={styles.stat}>
            <span className={styles.statLabel}>Total Revenue:</span>
            <span className={styles.statValue}>
              ${selectedBookings.reduce((sum, booking) => sum + (booking.revenue || 0), 0).toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className={styles.modalOverlay}>
          <div className={styles.confirmationModal}>
            <div className={styles.modalHeader}>
              <h3>Confirm Bulk Action</h3>
              <button
                onClick={cancelConfirmation}
                className={styles.closeButton}
                disabled={isProcessing}
              >
                ×
              </button>
            </div>
            
            <div className={styles.modalContent}>
              <div className={styles.warningIcon}>⚠️</div>
              <p className={styles.confirmationMessage}>
                {getConfirmationMessage()}
              </p>
              
              {pendingAction === 'cancel' && (
                <div className={styles.additionalWarning}>
                  <strong>Note:</strong> Customers will receive cancellation notifications automatically.
                </div>
              )}
              
              <div className={styles.selectedBookingsList}>
                <h4>Selected Bookings:</h4>
                <div className={styles.bookingsList}>
                  {selectedBookings.slice(0, 5).map(booking => (
                    <div key={booking.id} className={styles.bookingItem}>
                      <span className={styles.bookingRef}>
                        {booking.booking_reference || booking.id.slice(-8)}
                      </span>
                      <span className={styles.bookingCustomer}>
                        {booking.customerName}
                      </span>
                      <span className={styles.bookingService}>
                        {booking.serviceName}
                      </span>
                    </div>
                  ))}
                  {selectedBookings.length > 5 && (
                    <div className={styles.moreBookings}>
                      ... and {selectedBookings.length - 5} more
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <div className={styles.modalActions}>
              <button
                onClick={cancelConfirmation}
                className={styles.cancelButton}
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button
                onClick={confirmBulkAction}
                className={`${styles.confirmButton} ${styles[bulkActions.find(a => a.value === pendingAction)?.color || 'primary']}`}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <span className={styles.spinner}></span>
                    Processing...
                  </>
                ) : (
                  `Confirm ${bulkActions.find(a => a.value === pendingAction)?.label}`
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
