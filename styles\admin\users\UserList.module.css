.userListContainer {
  width: 100%;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.95rem;
  border-left: 4px solid #f44336;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.95rem;
  border-left: 4px solid #4caf50;
}

.filterContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.searchInput {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  width: 300px;
}

.searchInput input {
  flex: 1;
  border: none;
  padding: 8px;
  font-size: 0.9rem;
}

.searchInput input:focus {
  outline: none;
}

.searchIcon {
  color: #6c757d;
}

.filterOptions {
  display: flex;
  gap: 10px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.tableContainer {
  width: 100%;
  overflow-x: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.userTable {
  width: 100%;
  border-collapse: collapse;
}

.userTable th,
.userTable td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.userTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  cursor: pointer;
  transition: background-color 0.2s;
}

.userTable th:hover {
  background-color: #e9ecef;
}

.userTable tr:last-child td {
  border-bottom: none;
}

.userTable tr:hover {
  background-color: rgba(110, 142, 251, 0.05);
}

.sortIndicator {
  display: inline-block;
  margin-left: 4px;
  color: #6e8efb;
}

.actions {
  display: flex;
  gap: 8px;
}

.editButton {
  padding: 6px 12px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.cancelButton {
  padding: 6px 12px;
  background-color: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background-color: rgba(108, 117, 125, 0.1);
}

.saveButton {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover {
  background-color: #43a047;
}

.roleTag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.admin {
  background-color: rgba(106, 13, 173, 0.1);
  color: #6a0dad;
}

.staff {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.user {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.unknown {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.editRoleContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.editRoleSelect {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.85rem;
}

.editRoleActions {
  display: flex;
  gap: 8px;
}

.loading {
  padding: 40px 0;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.noUsers {
  padding: 40px 0;
  text-align: center;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.paginationInfo {
  font-size: 0.9rem;
  color: #6c757d;
}

.paginationButtons {
  display: flex;
  gap: 8px;
}

.paginationButton {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #f0f0f0;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationButton.active {
  background-color: #6e8efb;
  color: white;
  border-color: #6e8efb;
}
