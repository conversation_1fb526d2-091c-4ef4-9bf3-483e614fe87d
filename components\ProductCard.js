import { useState } from 'react';
import Link from 'next/link';
import styles from '@/styles/ProductCard.module.css';

const ProductCard = ({ product }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };
  
  return (
    <div className={`${styles.productCard} ${isExpanded ? styles.expanded : ''}`}>
      <div className={styles.cardContent}>
        {/* Card front content */}
        <div className={styles.cardFront}>
          <div className={styles.imageContainer}>
            <img 
              src={product.image} 
              alt={product.title}
              className={styles.productImage}
            />
            {product.isNewArrival && (
              <div className={styles.newBadge}>New</div>
            )}
          </div>
          <div className={styles.cardInfo}>
            <h3 className={styles.productTitle}>{product.title}</h3>
            <p className={styles.productPrice}>${product.price.toFixed(2)}</p>
            <p className={styles.productDescription}>{product.shortDescription}</p>
            <div className={styles.viewDetails} onClick={toggleExpand}>
              <span>View Details</span>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 9L12 16L5 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
        
        {/* Card expanded content */}
        <div className={styles.cardExpanded}>
          <h3 className={styles.expandedTitle}>{product.title}</h3>
          <p className={styles.expandedPrice}>${product.price.toFixed(2)}</p>
          <p className={styles.expandedDescription}>{product.description}</p>
          
          <div className={styles.featuresSection}>
            <h4>Key Features:</h4>
            <ul className={styles.featuresList}>
              {product.features.map((feature, idx) => (
                <li key={idx} className={styles.featureItem}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor"/>
                  </svg>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className={styles.detailsSection}>
            <h4>Product Details:</h4>
            <ul className={styles.detailsList}>
              {product.details.map((detail, idx) => (
                <li key={idx} className={styles.detailItem}>{detail}</li>
              ))}
            </ul>
          </div>
          
          <div className={styles.actionButtons}>
            <Link href={`/products/${product.id}`} className={styles.viewButton}>
              View Product
            </Link>
            <button className={styles.addToCartButton}>
              Add to Cart
            </button>
          </div>
          
          <div className={styles.closeExpanded} onClick={toggleExpand}>
            <span>Close</span>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 9L12 16L5 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" transform="rotate(180 12 12)"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
