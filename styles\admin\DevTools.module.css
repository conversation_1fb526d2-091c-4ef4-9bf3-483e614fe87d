/* Development Tools Styles */

.devToolsContainer {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.devToolsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.devToolsHeader h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.devToolsToggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.devToolsContent {
  font-size: 0.9rem;
  color: #555;
}

.devToolsActions {
  display: flex;
  gap: 10px;
  margin: 15px 0;
}

.testButton, .detailsButton {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.testButton {
  background-color: #6a0dad;
  color: white;
}

.testButton:hover {
  background-color: #5a0b8d;
}

.testButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.detailsButton {
  background-color: #f0f0f0;
  color: #333;
}

.detailsButton:hover {
  background-color: #e0e0e0;
}

.testResult {
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.success {
  background-color: #e6f7e6;
  border: 1px solid #c3e6c3;
  color: #2e7d32;
}

.error {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  color: #c62828;
}

.resultDetails {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.8rem;
  white-space: pre-wrap;
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #6a0dad;
}

input:focus + .slider {
  box-shadow: 0 0 1px #6a0dad;
}

input:checked + .slider:before {
  transform: translateX(20px);
}
