import { useState } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import UserForm from '@/components/admin/users/UserForm'
import styles from '@/styles/admin/UsersPage.module.css'

export default function NewUserPage() {
  const router = useRouter()
  const [error, setError] = useState(null)

  // Handle cancel button click
  const handleCancel = () => {
    router.push('/admin/users')
  }

  // Handle successful user creation
  const handleSuccess = () => {
    router.push('/admin/users')
  }

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="Add New User">
        <div className={styles.usersPage}>
          <div className={styles.header}>
            <h2>Add New User</h2>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <UserForm 
            onCancel={handleCancel}
            onSuccess={handleSuccess}
            setError={setError}
          />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
