.main {
  width: 100%;
  margin: 0 auto;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Keep background fixed during scroll */
}

/* Gallery Hero Section */
.galleryHero {
  padding: 6rem 2rem 4rem;
  text-align: center;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, rgba(85, 98, 255, 0.3) 100%);
  margin-bottom: 3rem;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.galleryTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color, #5562ff));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.gallerySubtitle {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

/* Category Filters */
.categoryFilters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.filterButton {
  padding: 0.8rem 1.5rem;
  background: transparent;
  border: 2px solid var(--primary-color);
  border-radius: 30px;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.filterButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.activeFilter {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

/* Gallery Grid */
.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem 5rem;
}

.galleryItem {
  background-color: rgba(255, 255, 255, 0.85); /* Translucent white */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  cursor: pointer;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
  display: flex;
  flex-direction: column;
  perspective: 1000px; /* 3D effect */
  transform-style: preserve-3d; /* 3D effect */
}

.galleryItem:hover {
  transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
}

.galleryImageContainer {
  position: relative;
  aspect-ratio: 1/1; /* Square aspect ratio for consistent layout */
  overflow: hidden;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.galleryItem:hover .galleryImage {
  transform: scale(1.08);
}

.galleryOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(78, 205, 196, 0) 0%,
    rgba(78, 205, 196, 0.7) 50%,
    rgba(78, 205, 196, 0.9) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.galleryItem:hover .galleryOverlay {
  opacity: 1;
}

.viewMore {
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  padding: 0.8rem 1.5rem;
  border: 2px solid white;
  border-radius: 30px;
  transform: translateY(30px) scale(0.9);
  transition: transform 0.5s ease, background-color 0.3s ease;
  background-color: rgba(0, 0, 0, 0.2);
}

.galleryItem:hover .viewMore {
  transform: translateY(0) scale(1);
}

.viewMore:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.galleryItemTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 1.2rem 1rem;
  color: var(--text-color);
  text-align: center;
  transition: transform 0.3s ease;
}

.galleryItem:hover .galleryItemTitle {
  transform: translateY(-5px);
}

/* Related Images Section */
.relatedImagesSection {
  padding: 5rem 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.1); /* More subtle border */
  background-color: rgba(255, 255, 255, 0.5); /* Translucent white background */
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.relatedTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.relatedSubtitle {
  font-size: 1.1rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto 3rem;
}

.relatedImagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.relatedImageItem {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  position: relative;
  background-color: rgba(255, 255, 255, 0.85); /* Translucent white */
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.relatedImageItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.relatedImage {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.relatedImageItem:hover .relatedImage {
  transform: scale(1.05);
}

.imageCaption {
  padding: 1rem;
  font-size: 1rem;
  color: var(--text-color);
  background-color: rgba(255, 255, 255, 0.85); /* Translucent white */
  text-align: center;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
  position: relative;
  z-index: 1;
}

/* Booking CTA */
.bookingCta {
  background-color: rgba(249, 249, 249, 0.8); /* Translucent light gray */
  padding: 3rem;
  border-radius: 12px;
  margin-top: 3rem;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.bookingCta h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.bookingCta p {
  font-size: 1.1rem;
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.ctaButton {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  margin: 0 0.5rem;
}

.ctaButton:hover {
  background-color: var(--secondary-color, #5562ff);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.secondaryButton {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.secondaryButton:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Gallery CTA Section */
.galleryCta {
  text-align: center;
  padding: 6rem 2rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, rgba(85, 98, 255, 0.3) 100%);
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.galleryCta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.galleryCta p {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto 2rem;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.primaryButton, .secondaryButton {
  display: inline-block;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.primaryButton {
  background-color: var(--primary-color);
  color: white;
}

.secondaryButton {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.primaryButton:hover, .secondaryButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.secondaryButton:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Lightbox */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.lightboxContent {
  position: relative;
  max-width: 95%;
  max-height: 95%;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: scaleIn 0.4s ease;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.lightboxImage {
  max-width: 100%;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease;
}

.lightboxCaption {
  color: white;
  margin-top: 1.5rem;
  font-size: 1.2rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  max-width: 80%;
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.closeButton {
  position: absolute;
  top: -50px;
  right: -50px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: rotate(90deg);
}

.lightboxNavigation {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: space-between;
  top: 50%;
  transform: translateY(-50%);
  padding: 0 1rem;
}

.navButton {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.navButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  opacity: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .galleryTitle {
    font-size: 3rem;
  }

  .galleryGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .galleryHero {
    padding: 5rem 1.5rem 3rem;
  }

  .galleryTitle {
    font-size: 2.5rem;
  }

  .gallerySubtitle {
    font-size: 1.1rem;
  }

  .relatedTitle {
    font-size: 2.2rem;
  }

  .bookingCta {
    padding: 2rem;
  }

  .bookingCta h3 {
    font-size: 1.6rem;
  }
}

@media (max-width: 480px) {
  .galleryHero {
    padding: 4rem 1rem 2.5rem;
  }

  .galleryTitle {
    font-size: 2.2rem;
  }

  .galleryGrid, .relatedImagesGrid {
    grid-template-columns: 1fr;
    padding: 0 1rem;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;
  }

  .primaryButton, .secondaryButton {
    width: 100%;
  }
}
