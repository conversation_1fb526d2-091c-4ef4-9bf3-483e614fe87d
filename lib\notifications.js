import { useEffect, useState } from 'react';
import supabase, { getAdminClient } from './supabase';
import { toast } from 'react-hot-toast';

// Custom hook to manage notifications
// Enhanced with better error handling, timeout mechanisms, and resource cleanup
export const useNotifications = (userId) => {
  const [notifications, setNotifications] = useState([]);
  const [newNotification, setNewNotification] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!userId) return;

    // Track if component is mounted
    let isMounted = true;
    let channel = null;
    let timeoutId = null;

    // Safe state update functions
    const safeSetNotifications = (value) => {
      if (isMounted) setNotifications(value);
    };

    const safeSetNewNotification = (value) => {
      if (isMounted) setNewNotification(value);
    };

    const safeSetError = (value) => {
      if (isMounted) setError(value);
    };

    const safeSetIsLoading = (value) => {
      if (isMounted) setIsLoading(value);
    };

    const initializeSubscription = async () => {
      // Set a timeout to prevent hanging initialization
      timeoutId = setTimeout(() => {
        if (isMounted) {
          console.error('Notification initialization timed out');
          safeSetError('Notification system initialization timed out');
          safeSetIsLoading(false);
        }
      }, 10000); // 10 second timeout

      try {
        safeSetIsLoading(true);

        // Use Supabase client directly
        let client;
        try {
          // Use the Supabase client directly
          client = supabase;

          if (!client) {
            throw new Error('Supabase client not available for notifications');
          }
        } catch (clientError) {
          console.error('Error getting Supabase client for notifications:', clientError);
          safeSetError(`Failed to initialize notification system: ${clientError.message}`);
          safeSetIsLoading(false);

          // Clear the timeout
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          return null;
        }

        // Fetch initial notifications with timeout
        try {
          const fetchPromise = client
            .from('notifications')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Notification fetch timeout')), 5000)
          );

          const { data, error } = await Promise.race([fetchPromise, timeoutPromise]);

          if (error) {
            console.error('Error fetching notifications:', error);
            safeSetError(`Error fetching notifications: ${error.message}`);
            // Continue with subscription even if initial fetch fails
          } else if (isMounted) {
            safeSetNotifications(data || []);
          }
        } catch (fetchError) {
          console.error('Exception during notification fetch:', fetchError);
          safeSetError(`Error fetching notifications: ${fetchError.message}`);
          // Continue with subscription even if initial fetch fails
        }

        // Subscribe to new notifications with error handling
        try {
          channel = client
            .channel(`notifications:${userId}`)
            .on(
              'postgres_changes',
              { event: 'INSERT', schema: 'public', table: 'notifications', filter: `user_id=eq.${userId}` },
              (payload) => {
                try {
                  if (!isMounted) return;

                  console.log('New notification received:', payload);
                  const newNotif = payload.new;

                  safeSetNotifications((prevNotifications) => [newNotif, ...prevNotifications]);
                  safeSetNewNotification(newNotif);

                  // Show toast notification if message exists
                  if (newNotif && newNotif.message) {
                    toast.success(newNotif.message);
                  } else {
                    toast.success('You have a new notification!');
                  }
                } catch (handlerError) {
                  console.error('Error handling new notification:', handlerError);
                  // Don't propagate the error to prevent breaking the subscription
                }
              }
            )
            .subscribe((status, err) => {
              if (!isMounted) return;

              if (status === 'SUBSCRIBED') {
                console.log('Subscribed to notifications for user:', userId);
                safeSetIsLoading(false);
                safeSetError(null);
              } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
                console.error('Notification subscription error:', status, err);
                safeSetError(`Notification subscription error: ${err?.message || status}`);
                safeSetIsLoading(false);

                // Show toast only for critical errors
                if (err?.message && err.message.includes('authentication')) {
                  toast.error(`Notification system error: ${err.message}`);
                }
              } else if (status === 'CLOSED') {
                console.log('Notification subscription closed for user:', userId);
                safeSetIsLoading(false);
              }
            });
        } catch (subscriptionError) {
          console.error('Error setting up notification subscription:', subscriptionError);
          safeSetError(`Error setting up notification subscription: ${subscriptionError.message}`);
          safeSetIsLoading(false);
        }

        // Clear the timeout since initialization completed
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        return () => {
          // Cleanup function for subscription
          if (channel && client) {
            try {
              client.removeChannel(channel).then(status => {
                console.log(`Channel for user ${userId} removed with status:`, status);
              }).catch(error => {
                console.error(`Error removing channel for user ${userId}:`, error);
              });
            } catch (cleanupError) {
              console.error('Error during notification channel cleanup:', cleanupError);
            }
          }
        };
      } catch (e) {
        // Clear the timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        console.error('Exception during notification subscription initialization:', e);
        safeSetError(`Critical error setting up notifications: ${e.message}`);
        safeSetIsLoading(false);

        // Show toast for critical errors
        toast.error(`Notification system error: ${e.message}`);
        return null;
      }
    };

    // Initialize the subscription
    const cleanupPromise = initializeSubscription();

    // Return cleanup function
    return () => {
      isMounted = false;

      // Clear any pending timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Execute cleanup function if available
      if (cleanupPromise) {
        cleanupPromise.then(cleanup => {
          if (typeof cleanup === 'function') {
            try {
              cleanup();
            } catch (cleanupError) {
              console.error('Error during notification cleanup execution:', cleanupError);
            }
          }
        }).catch(e => console.error('Error during notification cleanup promise resolution:', e));
      }
    };
  }, [userId]);

  // Return the hook state and functions
  return {
    notifications,
    newNotification,
    error,
    isLoading
  };
};

// Mark a notification as read
// Enhanced with better error handling and timeout protection
export const markNotificationAsRead = async (notificationId) => {
  // Create a timeout ID for cleanup
  let timeoutId = null;

  try {
    // Use Supabase client directly
    let client;
    try {
      // Use the Supabase client directly
      client = supabase;

      if (!client) {
        throw new Error('Supabase client not available for marking notification as read');
      }
    } catch (clientError) {
      console.error('Error getting Supabase client for marking notification as read:', clientError);
      toast.error(`Error: ${clientError.message}`);
      return null;
    }

    // Update notification with timeout protection
    try {
      const updatePromise = client
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .select(); // select() to get the updated record back

      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => reject(new Error('Update notification timeout')), 5000);
      });

      const { data, error } = await Promise.race([updatePromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (error) {
        console.error('Error marking notification as read:', error);
        toast.error(`Error: ${error.message}`);
        return null;
      }

      console.log('Notification marked as read:', data);
      return data;
    } catch (updateError) {
      console.error('Error updating notification:', updateError);
      toast.error(`Failed to mark notification as read: ${updateError.message}`);
      return null;
    }
  } catch (error) {
    // Clear any pending timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    console.error('Exception in markNotificationAsRead:', error);
    toast.error(`Failed to mark notification as read: ${error.message}`);
    return null;
  }
};

// Clear all notifications for a user
// Enhanced with better error handling and timeout protection
export const clearAllNotifications = async (userId) => {
  // Create a timeout ID for cleanup
  let timeoutId = null;

  try {
    // Use Supabase client directly
    let client;
    try {
      // Use the Supabase client directly
      client = supabase;

      if (!client) {
        throw new Error('Supabase client not available for clearing notifications');
      }
    } catch (clientError) {
      console.error('Error getting Supabase client for clearing notifications:', clientError);
      toast.error(`Error: ${clientError.message}`);
      return false;
    }

    // Delete notifications with timeout protection
    try {
      const deletePromise = client
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => reject(new Error('Delete notifications timeout')), 5000);
      });

      const { error } = await Promise.race([deletePromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (error) {
        console.error('Error clearing notifications:', error);
        toast.error(`Error: ${error.message}`);
        return false;
      }

      toast.success('All notifications cleared.');
      return true;
    } catch (deleteError) {
      console.error('Error deleting notifications:', deleteError);
      toast.error(`Failed to clear notifications: ${deleteError.message}`);
      return false;
    }
  } catch (error) {
    // Clear any pending timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    console.error('Exception in clearAllNotifications:', error);
    toast.error(`Failed to clear notifications: ${error.message}`);
    return false;
  }
};

/**
 * Send email notification using OneSignal
 * @param {Object} params - Notification parameters
 * @param {string} params.email - Recipient email
 * @param {string} params.subject - Email subject
 * @param {string} params.message - Plain text message
 * @param {string} params.htmlBody - HTML body (optional)
 * @param {Object} params.data - Additional data to include in the notification
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOneSignalEmail = async ({ email, subject, message, htmlBody, data = {} }) => {
  try {
    // Skip actual sending in development and just log
    if (process.env.NODE_ENV === 'development' || !process.env.ONESIGNAL_REST_API_KEY) {
      console.log('Development mode: Email would be sent', {
        email, subject, message, htmlBody, data
      });
      return { success: true, development: true };
    }

    // For production, send actual email via OneSignal REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify({
        app_id: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
        include_email_tokens: [email],
        email_subject: subject,
        email_body: htmlBody || message,
        email_from_name: 'OceanSoulSparkles',
        channel_for_external_user_ids: 'email',
        data
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`OneSignal API error: ${JSON.stringify(result)}`);
    }

    return { success: true, notification_id: result.id };
  } catch (error) {
    console.error('Error sending email notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send push notification using OneSignal
 * @param {Object} params - Notification parameters
 * @param {Array<string>} params.userIds - Array of user IDs to send the notification to
 * @param {string} params.title - Notification title
 * @param {string} params.message - Notification message
 * @param {Object} params.data - Additional data to include in the notification
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOneSignalPush = async ({ userIds, title, message, data = {} }) => {
  try {
    // Skip actual sending in development and just log
    if (process.env.NODE_ENV === 'development' || !process.env.ONESIGNAL_REST_API_KEY) {
      console.log('Development mode: Push notification would be sent', {
        userIds, title, message, data
      });
      return { success: true, development: true };
    }

    // For production, send actual push notification via OneSignal REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify({
        app_id: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
        include_external_user_ids: userIds,
        headings: { en: title },
        contents: { en: message },
        data,
        channel_for_external_user_ids: 'push'
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`OneSignal API error: ${JSON.stringify(result)}`);
    }

    return { success: true, notification_id: result.id };
  } catch (error) {
    console.error('Error sending push notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send booking notification to customer
 * @param {Object} params - Booking notification parameters
 * @param {string} params.bookingId - Booking ID
 * @param {string} params.customerId - Customer ID
 * @param {string} params.customerEmail - Customer email
 * @param {string} params.customerName - Customer name
 * @param {string} params.customerPhone - Customer phone (used in message templates)
 * @param {string} params.status - Booking status
 * @param {string} params.startTime - Booking start time
 * @param {string} params.serviceName - Service name
 * @param {string} params.location - Booking location
 * @param {boolean} params.marketingConsent - Whether customer has consented to marketing (for future use)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendBookingNotification = async ({
  bookingId,
  customerId,
  customerEmail,
  customerName,
  status,
  startTime,
  serviceName,
  location,
  customerPhone = '', // Optional parameter, not used directly in this function
  marketingConsent = false // For future marketing integrations
}) => {
  try {
    // Format date for display
    const bookingDate = new Date(startTime);
    const formattedDate = bookingDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formattedTime = bookingDate.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    // Create notification title and content based on status
    let title, message;

    switch(status) {
      case 'confirmed':
        title = 'Booking Confirmed';
        message = `Your booking for ${serviceName} has been confirmed for ${formattedDate} at ${formattedTime}. Location: ${location}.`;
        break;
      case 'pending':
        title = 'Booking Received';
        message = `We've received your booking request for ${serviceName} on ${formattedDate} at ${formattedTime}. We'll confirm shortly.`;
        break;
      case 'cancelled':
        title = 'Booking Cancelled';
        message = `Your booking for ${serviceName} on ${formattedDate} at ${formattedTime} has been cancelled.`;
        break;
      case 'completed':
        title = 'Booking Completed';
        message = `Thank you for visiting OceanSoulSparkles. Your ${serviceName} booking has been marked as completed.`;
        break;
      default:
        title = 'Booking Update';
        message = `Your booking for ${serviceName} on ${formattedDate} at ${formattedTime} has been updated.`;
    }

    // Record notification in database
    try {
      await supabase
        .from('notifications')
        .insert([
          {
            title,
            message,
            notification_type: 'booking',
            related_id: bookingId,
            user_id: customerId
          }
        ]);
    } catch (error) {
      console.error('Error recording booking notification:', error);
      // Continue with sending notification even if recording fails
    }

    // Send email notification
    if (customerEmail) {
      await sendOneSignalEmail({
        email: customerEmail,
        subject: title,
        message,
        htmlBody: `<div>
          <h2>${title}</h2>
          <p>Hello ${customerName},</p>
          <p>${message}</p>
          <p>Booking details:</p>
          <ul>
            <li>Service: ${serviceName}</li>
            <li>Date: ${formattedDate}</li>
            <li>Time: ${formattedTime}</li>
            <li>Location: ${location}</li>
            <li>Status: ${status}</li>
          </ul>
          <p>For any questions, please contact us.</p>
          <p>Thank you for choosing OceanSoulSparkles.</p>
        </div>`,
        data: {
          type: 'booking_notification',
          booking_id: bookingId,
          status
        }
      });
    }

    // Send push notification if customer has a user ID
    if (customerId) {
      await sendOneSignalPush({
        userIds: [customerId],
        title,
        message,
        data: {
          type: 'booking_notification',
          booking_id: bookingId,
          status
        }
      });
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending booking notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send order notification to customer
 * @param {Object} params - Order notification parameters
 * @param {string} params.orderId - Order ID
 * @param {string} params.customerEmail - Customer email
 * @param {string} params.customerName - Customer name
 * @param {string} params.status - Order status
 * @param {number} params.total - Order total amount
 * @param {Array} params.items - Order items
 * @param {boolean} params.marketingConsent - Whether customer has consented to marketing (for future use)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOrderNotification = async ({
  orderId,
  customerEmail,
  customerName,
  status,
  total,
  items = [],
  marketingConsent = false // For future marketing integrations
}) => {
  try {
    // Format order date
    const orderDate = new Date();
    const formattedDate = orderDate.toLocaleDateString('en-AU');

    // Create notification title and content based on status
    let title, message;

    switch(status) {
      case 'confirmed':
        title = 'Order Confirmed';
        message = `Your order #${orderId} has been confirmed. Total: $${total.toFixed(2)}.`;
        break;
      case 'processing':
        title = 'Order Processing';
        message = `Your order #${orderId} is now being processed. We'll let you know when it ships.`;
        break;
      case 'shipped':
        title = 'Order Shipped';
        message = `Great news! Your order #${orderId} has been shipped and is on its way to you.`;
        break;
      case 'delivered':
        title = 'Order Delivered';
        message = `Your order #${orderId} has been delivered. We hope you enjoy your purchase!`;
        break;
      case 'cancelled':
        title = 'Order Cancelled';
        message = `Your order #${orderId} has been cancelled. If you have any questions, please contact us.`;
        break;
      default:
        title = 'Order Update';
        message = `Your order #${orderId} has been updated to status: ${status}.`;
    }

    // Create HTML for order items
    let itemsHtml = '';
    if (items && items.length > 0) {
      itemsHtml = '<ul>';
      items.forEach(item => {
        itemsHtml += `<li>${item.quantity}x ${item.name} - $${(item.price * item.quantity).toFixed(2)}</li>`;
      });
      itemsHtml += '</ul>';
    }

    // Send email notification
    if (customerEmail) {
      await sendOneSignalEmail({
        email: customerEmail,
        subject: title,
        message,
        htmlBody: `<div>
          <h2>${title}</h2>
          <p>Hello ${customerName},</p>
          <p>${message}</p>
          <p>Order details:</p>
          <p><strong>Order #:</strong> ${orderId}</p>
          <p><strong>Date:</strong> ${formattedDate}</p>
          <p><strong>Status:</strong> ${status}</p>
          <p><strong>Items:</strong></p>
          ${itemsHtml}
          <p><strong>Total:</strong> $${total.toFixed(2)}</p>
          <p>Thank you for shopping with OceanSoulSparkles!</p>
        </div>`,
        data: {
          type: 'order_notification',
          order_id: orderId,
          status
        }
      });
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending order notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send a notification to a customer
 * @param {Object} params - Notification parameters
 * @param {string} params.customerId - Customer ID
 * @param {string} params.title - Notification title
 * @param {string} params.message - Notification message
 * @param {string} params.type - Notification type (email, sms, push)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendCustomerNotification = async ({ customerId, title, message, type = 'email' }) => {
  try {
    // Get customer details using Supabase client
    const { data: customer, error } = await supabase
      .from('customers')
      .select('id, name, email, phone')
      .eq('id', customerId)
      .single();

    if (error) throw error;
    if (!customer) throw new Error(`Customer with ID ${customerId} not found.`);

    // Send notification based on type
    if (type === 'email' && customer.email) {
      return await sendOneSignalEmail({
        email: customer.email,
        subject: title,
        message: message,
        htmlBody: `<div>
          <h2>${title}</h2>
          <p>Hello ${customer.name},</p>
          <p>${message}</p>
          <p>Thank you for choosing OceanSoulSparkles.</p>
        </div>`,
        data: {
          type: 'customer_notification',
          customer_id: customerId
        }
      });
    } else if (type === 'sms' && customer.phone) {
      // In a real implementation, this would use an SMS service
      console.log(`SMS would be sent to ${customer.phone}:`, message);
      return { success: true, development: true };
    } else if (type === 'push') {
      return await sendOneSignalPush({
        userIds: [customerId],
        title,
        message,
        data: {
          type: 'customer_notification',
          customer_id: customerId
        }
      });
    }

    throw new Error(`Unable to send ${type} notification to customer. Required contact information missing.`);
  } catch (error) {
    console.error('Error sending customer notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send booking reminder notification to customer
 * @param {Object} params - Booking reminder notification parameters
 * @param {string} params.bookingId - Booking ID
 * @param {string} params.customerId - Customer ID
 * @param {string} params.startTime - Booking start time
 * @param {string} params.serviceName - Service name
 * @param {string} params.location - Booking location
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendBookingReminderNotification = async ({
  bookingId,
  customerId,
  startTime,
  serviceName,
  location
}) => {
  try {
    // Get customer details using Supabase client
    const { data: customer, error } = await supabase
      .from('customers')
      .select('id, name, email, phone')
      .eq('id', customerId)
      .single();

    if (error) throw error;
    if (!customer) throw new Error(`Customer with ID ${customerId} not found.`);

    // Format date for display
    const bookingDate = new Date(startTime);
    const formattedDate = bookingDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formattedTime = bookingDate.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    // Create notification title and content
    const title = 'Booking Reminder';
    const message = `Reminder: You have a booking for ${serviceName} tomorrow (${formattedDate}) at ${formattedTime}. Location: ${location}.`;

    // Record notification in database
    try {
      await supabase
        .from('notifications')
        .insert([
          {
            title,
            message,
            notification_type: 'booking_reminder',
            related_id: bookingId,
            user_id: customerId
          }
        ]);
    } catch (dbError) {
      console.error('Error recording booking reminder notification:', dbError);
      // Continue with sending notification even if recording fails
    }

    // Send email notification
    if (customer.email) {
      await sendOneSignalEmail({
        email: customer.email,
        subject: title,
        message,
        htmlBody: `<div>
          <h2>${title}</h2>
          <p>Hello ${customer.name},</p>
          <p>${message}</p>
          <p>Booking details:</p>
          <ul>
            <li>Service: ${serviceName}</li>
            <li>Date: ${formattedDate}</li>
            <li>Time: ${formattedTime}</li>
            <li>Location: ${location}</li>
          </ul>
          <p>For any questions, please contact us.</p>
          <p>Thank you for choosing OceanSoulSparkles.</p>
        </div>`,
        data: {
          type: 'booking_reminder',
          booking_id: bookingId
        }
      });
    }

    // Send push notification if customer has a user ID
    if (customerId) {
      await sendOneSignalPush({
        userIds: [customerId],
        title,
        message,
        data: {
          type: 'booking_reminder',
          booking_id: bookingId
        }
      });
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending booking reminder notification:', error);
    return { success: false, error: error.message };
  }
};
