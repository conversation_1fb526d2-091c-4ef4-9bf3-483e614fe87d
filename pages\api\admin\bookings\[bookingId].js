import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';
import { sendBookingNotification } from '@/lib/notifications';

/**
 * Individual Booking Management API
 * 
 * GET /api/admin/bookings/{bookingId}
 * PUT /api/admin/bookings/{bookingId}
 * DELETE /api/admin/bookings/{bookingId}
 * 
 * Manages individual booking operations
 * Used by EnhancedBookingDetails component for status updates and modifications
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Individual booking request started`);

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract booking ID from URL
    const { bookingId } = req.query;

    if (!bookingId) {
      return res.status(400).json({ error: 'Booking ID is required' });
    }

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getBooking(bookingId, requestId, res);
      case 'PUT':
        return await updateBooking(bookingId, req.body, authResult.user, requestId, res);
      case 'DELETE':
        return await deleteBooking(bookingId, authResult.user, requestId, res);
      default:
        console.log(`[${requestId}] Method ${req.method} not allowed`);
        return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in individual booking API:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while managing booking'
    });
  }
}

// Get a single booking with full details
async function getBooking(bookingId, requestId, res) {
  console.log(`[${requestId}] Fetching booking: ${bookingId}`);

  const { data: booking, error } = await supabaseAdmin
    .from('bookings')
    .select(`
      id,
      customer_id,
      service_id,
      start_time,
      end_time,
      status,
      location,
      notes,
      booking_reference,
      estimated_revenue,
      actual_revenue,
      priority_level,
      internal_notes,
      customer_notes,
      created_at,
      updated_at,
      customers:customer_id (
        id,
        name,
        email,
        phone
      ),
      services:service_id (
        id,
        name,
        color,
        price,
        duration
      )
    `)
    .eq('id', bookingId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return res.status(404).json({ error: 'Booking not found' });
    }
    console.error(`[${requestId}] Error fetching booking:`, error);
    throw error;
  }

  // Transform the data for frontend consumption
  const transformedBooking = {
    id: booking.id,
    customer_id: booking.customer_id,
    service_id: booking.service_id,
    start_time: booking.start_time,
    end_time: booking.end_time,
    status: booking.status,
    location: booking.location,
    notes: booking.notes,
    booking_reference: booking.booking_reference,
    estimated_revenue: booking.estimated_revenue,
    actual_revenue: booking.actual_revenue,
    priority_level: booking.priority_level,
    internal_notes: booking.internal_notes,
    customer_notes: booking.customer_notes,
    created_at: booking.created_at,
    updated_at: booking.updated_at,
    // Flatten customer and service data
    customerName: booking.customers?.name || 'Unknown Customer',
    customerEmail: booking.customers?.email || '',
    customerPhone: booking.customers?.phone || '',
    serviceName: booking.services?.name || 'Unknown Service',
    serviceColor: booking.services?.color || '#3788d8',
    servicePrice: booking.services?.price || 0,
    serviceDuration: booking.services?.duration || 60,
    // Add computed fields
    revenue: booking.actual_revenue || booking.estimated_revenue || booking.services?.price || 0,
    duration: booking.services?.duration || 60
  };

  console.log(`[${requestId}] Booking fetched successfully`);
  return res.status(200).json({ booking: transformedBooking });
}

// Update a booking
async function updateBooking(bookingId, body, user, requestId, res) {
  console.log(`[${requestId}] Updating booking: ${bookingId}`);

  // First, get the current booking to track changes
  const { data: currentBooking, error: fetchError } = await supabaseAdmin
    .from('bookings')
    .select('*')
    .eq('id', bookingId)
    .single();

  if (fetchError) {
    if (fetchError.code === 'PGRST116') {
      return res.status(404).json({ error: 'Booking not found' });
    }
    throw fetchError;
  }

  // Extract updateable fields from body
  const {
    customer_id,
    service_id,
    start_time,
    end_time,
    status,
    location,
    notes,
    estimated_revenue,
    actual_revenue,
    priority_level,
    internal_notes,
    customer_notes
  } = body;

  // Build update object with only provided fields
  const updateData = {
    updated_at: new Date().toISOString()
  };

  if (customer_id !== undefined) updateData.customer_id = customer_id;
  if (service_id !== undefined) updateData.service_id = service_id;
  if (start_time !== undefined) updateData.start_time = new Date(start_time).toISOString();
  if (end_time !== undefined) updateData.end_time = new Date(end_time).toISOString();
  if (status !== undefined) updateData.status = status;
  if (location !== undefined) updateData.location = location;
  if (notes !== undefined) updateData.notes = notes;
  if (estimated_revenue !== undefined) updateData.estimated_revenue = estimated_revenue;
  if (actual_revenue !== undefined) updateData.actual_revenue = actual_revenue;
  if (priority_level !== undefined) updateData.priority_level = priority_level;
  if (internal_notes !== undefined) updateData.internal_notes = internal_notes;
  if (customer_notes !== undefined) updateData.customer_notes = customer_notes;

  // Validate status if provided
  if (status) {
    const validStatuses = ['pending', 'confirmed', 'in_progress', 'completed', 'canceled', 'no_show'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ 
        error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` 
      });
    }
  }

  // Update the booking
  const { data: updatedBooking, error: updateError } = await supabaseAdmin
    .from('bookings')
    .update(updateData)
    .eq('id', bookingId)
    .select(`
      id,
      customer_id,
      service_id,
      start_time,
      end_time,
      status,
      location,
      notes,
      booking_reference,
      estimated_revenue,
      actual_revenue,
      priority_level,
      internal_notes,
      customer_notes,
      created_at,
      updated_at,
      customers:customer_id (
        id,
        name,
        email,
        phone
      ),
      services:service_id (
        id,
        name,
        color,
        price,
        duration
      )
    `)
    .single();

  if (updateError) {
    console.error(`[${requestId}] Error updating booking:`, updateError);
    throw updateError;
  }

  // Log status change if status was updated
  if (status && status !== currentBooking.status) {
    try {
      // Create status history record if the table exists
      await supabaseAdmin
        .from('booking_status_history')
        .insert([{
          booking_id: bookingId,
          previous_status: currentBooking.status,
          new_status: status,
          changed_by: user.id,
          notes: `Status changed via admin interface`
        }]);
    } catch (historyError) {
      // Don't fail the update if history logging fails
      console.warn(`[${requestId}] Failed to log status history:`, historyError);
    }

    // Send notification if status changed and customer has email
    if (updatedBooking.customers?.email) {
      try {
        await sendBookingNotification({
          bookingId: bookingId,
          customerId: updatedBooking.customer_id,
          status: status,
          startTime: updatedBooking.start_time,
          serviceName: updatedBooking.services?.name || 'Service'
        });
      } catch (notificationError) {
        // Don't fail the update if notification fails
        console.warn(`[${requestId}] Failed to send notification:`, notificationError);
      }
    }
  }

  // Transform the response data
  const transformedBooking = {
    id: updatedBooking.id,
    customer_id: updatedBooking.customer_id,
    service_id: updatedBooking.service_id,
    start_time: updatedBooking.start_time,
    end_time: updatedBooking.end_time,
    status: updatedBooking.status,
    location: updatedBooking.location,
    notes: updatedBooking.notes,
    booking_reference: updatedBooking.booking_reference,
    estimated_revenue: updatedBooking.estimated_revenue,
    actual_revenue: updatedBooking.actual_revenue,
    priority_level: updatedBooking.priority_level,
    internal_notes: updatedBooking.internal_notes,
    customer_notes: updatedBooking.customer_notes,
    created_at: updatedBooking.created_at,
    updated_at: updatedBooking.updated_at,
    // Flatten customer and service data
    customerName: updatedBooking.customers?.name || 'Unknown Customer',
    customerEmail: updatedBooking.customers?.email || '',
    customerPhone: updatedBooking.customers?.phone || '',
    serviceName: updatedBooking.services?.name || 'Unknown Service',
    serviceColor: updatedBooking.services?.color || '#3788d8',
    servicePrice: updatedBooking.services?.price || 0,
    serviceDuration: updatedBooking.services?.duration || 60,
    // Add computed fields
    revenue: updatedBooking.actual_revenue || updatedBooking.estimated_revenue || updatedBooking.services?.price || 0,
    duration: updatedBooking.services?.duration || 60
  };

  console.log(`[${requestId}] Booking updated successfully`);
  return res.status(200).json({ booking: transformedBooking });
}

// Delete a booking
async function deleteBooking(bookingId, user, requestId, res) {
  console.log(`[${requestId}] Deleting booking: ${bookingId}`);

  // First check if booking exists
  const { data: booking, error: fetchError } = await supabaseAdmin
    .from('bookings')
    .select('id, status, customers:customer_id(email)')
    .eq('id', bookingId)
    .single();

  if (fetchError) {
    if (fetchError.code === 'PGRST116') {
      return res.status(404).json({ error: 'Booking not found' });
    }
    throw fetchError;
  }

  // Delete the booking
  const { error: deleteError } = await supabaseAdmin
    .from('bookings')
    .delete()
    .eq('id', bookingId);

  if (deleteError) {
    console.error(`[${requestId}] Error deleting booking:`, deleteError);
    throw deleteError;
  }

  console.log(`[${requestId}] Booking deleted successfully`);
  return res.status(200).json({ 
    message: 'Booking deleted successfully',
    deletedBookingId: bookingId 
  });
}
