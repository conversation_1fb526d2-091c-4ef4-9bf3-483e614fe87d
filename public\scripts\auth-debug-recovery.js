/**
 * Authentication Debug and Recovery System
 * 
 * This script provides comprehensive debugging and recovery for authentication issues,
 * specifically targeting white screen problems during login.
 */

(function() {
  'use strict';

  // Configuration
  const DEBUG = true;
  const AUTH_DEBUG_PREFIX = '[Auth Debug]';
  
  // Track authentication state
  let authState = {
    initialized: false,
    user: null,
    errors: [],
    lastActivity: Date.now()
  };

  // Enhanced logging function
  function authLog(message, data = null) {
    if (DEBUG) {
      console.log(`${AUTH_DEBUG_PREFIX} ${message}`, data || '');
    }
  }

  // Error tracking function
  function trackError(error, context = 'unknown') {
    const errorInfo = {
      message: error.message || error,
      context,
      timestamp: Date.now(),
      stack: error.stack || 'No stack trace'
    };
    
    authState.errors.push(errorInfo);
    authLog(`Error in ${context}:`, errorInfo);
    
    // Keep only last 10 errors
    if (authState.errors.length > 10) {
      authState.errors.shift();
    }
  }

  // Monitor for white screen (no visible content)
  function checkForWhiteScreen() {
    const body = document.body;
    const hasVisibleContent = body && (
      body.children.length > 0 &&
      body.offsetHeight > 0 &&
      body.offsetWidth > 0
    );

    if (!hasVisibleContent) {
      authLog('Potential white screen detected!');
      return true;
    }
    return false;
  }

  // Monitor authentication initialization
  function monitorAuthInit() {
    authLog('Starting authentication monitoring...');

    // Check for React/Next.js errors
    window.addEventListener('error', function(event) {
      if (event.error) {
        trackError(event.error, 'Global Error Handler');
        
        // Check if this error might cause a white screen
        if (event.error.message.includes('auth') || 
            event.error.message.includes('supabase') ||
            event.error.message.includes('context')) {
          authLog('Authentication-related error detected:', event.error.message);
          
          // Try to recover after a short delay
          setTimeout(attemptRecovery, 1000);
        }
      }
    });

    // Monitor unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      if (event.reason) {
        trackError(event.reason, 'Unhandled Promise Rejection');
        
        if (event.reason.message && (
            event.reason.message.includes('auth') ||
            event.reason.message.includes('supabase'))) {
          authLog('Authentication promise rejection:', event.reason.message);
          setTimeout(attemptRecovery, 1000);
        }
      }
    });

    // Monitor for white screen every 2 seconds
    setInterval(function() {
      if (checkForWhiteScreen() && document.readyState === 'complete') {
        authLog('White screen persisting after page load complete');
        setTimeout(attemptRecovery, 500);
      }
    }, 2000);
  }

  // Recovery attempt function
  function attemptRecovery() {
    authLog('Attempting authentication recovery...');

    try {
      // Clear potentially corrupted auth data
      if (typeof localStorage !== 'undefined') {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('supabase') || key.includes('auth'))) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          authLog(`Clearing localStorage key: ${key}`);
          localStorage.removeItem(key);
        });
      }

      // Clear session storage
      if (typeof sessionStorage !== 'undefined') {
        const sessionKeysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (key.includes('supabase') || key.includes('auth'))) {
            sessionKeysToRemove.push(key);
          }
        }
        
        sessionKeysToRemove.forEach(key => {
          authLog(`Clearing sessionStorage key: ${key}`);
          sessionStorage.removeItem(key);
        });
      }

      // Try to reinitialize authentication if Supabase is available
      if (window.supabase && typeof window.supabase.auth !== 'undefined') {
        authLog('Attempting to reinitialize Supabase auth...');
        
        window.supabase.auth.getSession().then(function(response) {
          authLog('Supabase session check result:', response);
        }).catch(function(error) {
          authLog('Supabase session check failed:', error);
        });
      }

      // If still white screen after recovery attempt, show manual recovery option
      setTimeout(function() {
        if (checkForWhiteScreen()) {
          showManualRecoveryOption();
        }
      }, 3000);

    } catch (recoveryError) {
      trackError(recoveryError, 'Recovery Attempt');
    }
  }

  // Show manual recovery option
  function showManualRecoveryOption() {
    authLog('Showing manual recovery option...');

    // Create recovery UI if it doesn't exist
    if (!document.getElementById('auth-manual-recovery')) {
      const recoveryDiv = document.createElement('div');
      recoveryDiv.id = 'auth-manual-recovery';
      recoveryDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #e74c3c;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        max-width: 400px;
        text-align: center;
      `;

      recoveryDiv.innerHTML = `
        <h3 style="margin-top: 0; color: #e74c3c;">Authentication Recovery</h3>
        <p>The admin panel is experiencing loading issues. Try these recovery options:</p>
        <button id="clear-cache-btn" style="
          background: #3498db;
          color: white;
          border: none;
          padding: 10px 15px;
          border-radius: 4px;
          cursor: pointer;
          margin: 5px;
        ">Clear Cache & Reload</button>
        <button id="force-reload-btn" style="
          background: #27ae60;
          color: white;
          border: none;
          padding: 10px 15px;
          border-radius: 4px;
          cursor: pointer;
          margin: 5px;
        ">Force Reload</button>
        <button id="show-errors-btn" style="
          background: #f39c12;
          color: white;
          border: none;
          padding: 10px 15px;
          border-radius: 4px;
          cursor: pointer;
          margin: 5px;
        ">Show Errors</button>
        <div id="error-display" style="
          margin-top: 15px;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 4px;
          text-align: left;
          font-size: 12px;
          max-height: 200px;
          overflow-y: auto;
          display: none;
        "></div>
      `;

      document.body.appendChild(recoveryDiv);

      // Add event listeners
      document.getElementById('clear-cache-btn').addEventListener('click', function() {
        authLog('User triggered cache clear');
        attemptRecovery();
        setTimeout(() => window.location.reload(), 1000);
      });

      document.getElementById('force-reload-btn').addEventListener('click', function() {
        authLog('User triggered force reload');
        window.location.reload(true);
      });

      document.getElementById('show-errors-btn').addEventListener('click', function() {
        const errorDisplay = document.getElementById('error-display');
        if (errorDisplay.style.display === 'none') {
          errorDisplay.style.display = 'block';
          errorDisplay.innerHTML = authState.errors.map(error => 
            `<div><strong>${error.context}:</strong> ${error.message}</div>`
          ).join('') || 'No errors recorded';
        } else {
          errorDisplay.style.display = 'none';
        }
      });
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', monitorAuthInit);
  } else {
    monitorAuthInit();
  }

  // Make debug info available globally
  window.authDebugInfo = function() {
    return {
      state: authState,
      isWhiteScreen: checkForWhiteScreen(),
      timestamp: Date.now()
    };
  };

  authLog('Authentication debug system initialized');
})();
