#!/usr/bin/env python3

import pandas as pd
import re

# Load and debug the CSV
df = pd.read_csv('wixmigratedata/cleaned_invoices.csv')

print("Original column names:")
print(df.columns.tolist())

# Clean column names
df.columns = [col.replace('="', '').replace('"', '') for col in df.columns]
print("\nCleaned column names:")
print(df.columns.tolist())

print("\nFirst few rows:")
print(df.head())

# Check the first customer value
print(f"\nFirst customer value: '{df.iloc[0]['Customer']}'")
print(f"Type: {type(df.iloc[0]['Customer'])}")

# Try cleaning
customer_name = str(df.iloc[0]['Customer']).strip()
print(f"After str(): '{customer_name}'")

customer_name = re.sub(r'^"=""?|""?$', '', customer_name)
print(f"After regex: '{customer_name}'")
