.settingsPage {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.errorBox {
  background-color: #fed7d7;
  border: 1px solid #f56565;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.errorBox p {
  color: #c53030;
  margin: 0;
}

.errorBox button {
  background-color: #fff;
  border: 1px solid #c53030;
  color: #c53030;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.errorBox button:hover {
  background-color: #c53030;
  color: #fff;
}

.successBox {
  background-color: #c6f6d5;
  border: 1px solid #48bb78;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.successBox p {
  color: #2f855a;
  margin: 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.125rem;
  color: #4a5568;
}
