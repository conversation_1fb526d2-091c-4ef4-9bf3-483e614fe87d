/**
 * Browser Extension Error Suppression - Enhanced Version
 *
 * This script aggressively suppresses browser extension errors that can interfere
 * with the application, including runtime.lastError messages.
 */

(function() {
  'use strict';

  // Suppress console errors from extensions
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  console.error = function(...args) {
    const message = args.join(' ');

    // Suppress specific extension error patterns
    const suppressedPatterns = [
      'runtime.lastError',
      'message port closed',
      'Extension context invalidated',
      'chrome-extension://',
      'moz-extension://',
      'Unchecked runtime.lastError',
      'The message port closed before a response was received',
      'feedback.html',
      'admin:1 Unchecked runtime.lastError',
      'login:1 Unchecked runtime.lastError',
      'marketing:1 Unchecked runtime.lastError',
      'analytics:1 Unchecked runtime.lastError'
    ];

    if (suppressedPatterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()))) {
      console.debug('[Extension Error Suppressed]:', message);
      return;
    }

    // Call original console.error for legitimate errors
    return originalConsoleError.apply(console, args);
  };

  console.warn = function(...args) {
    const message = args.join(' ');

    // Suppress extension warnings too
    const suppressedPatterns = [
      'runtime.lastError',
      'message port closed',
      'Extension context invalidated',
      'chrome-extension://',
      'moz-extension://'
    ];

    if (suppressedPatterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()))) {
      console.debug('[Extension Warning Suppressed]:', message);
      return;
    }

    // Call original console.warn for legitimate warnings
    return originalConsoleWarn.apply(console, args);
  };

  // Suppress runtime.lastError at the Chrome API level
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    // Override chrome.runtime.lastError getter
    let lastErrorValue = null;
    Object.defineProperty(chrome.runtime, 'lastError', {
      get: function() {
        const error = lastErrorValue;
        lastErrorValue = null; // Clear after reading
        return error;
      },
      set: function(value) {
        if (value && value.message && value.message.includes('message port closed')) {
          console.debug('[Chrome Runtime Error Suppressed]:', value.message);
          return; // Don't set the error
        }
        lastErrorValue = value;
      },
      configurable: true
    });

    // Enhance message listener error handling
    const originalAddListener = chrome.runtime.onMessage.addListener;
    if (originalAddListener) {
      chrome.runtime.onMessage.addListener = function(callback) {
        return originalAddListener.call(this, function(...args) {
          try {
            return callback(...args);
          } catch (error) {
            if (error.message && error.message.includes('message port closed')) {
              console.debug('[Extension Message Error Suppressed]:', error.message);
              return;
            }
            throw error;
          }
        });
      };
    }
  }

  // Global error handler for unhandled extension errors
  window.addEventListener('error', function(event) {
    const error = event.error;
    const message = event.message || '';
    const filename = event.filename || '';

    // Suppress common extension-related errors
    const suppressedErrors = [
      'message port closed',
      'Extension context invalidated',
      'runtime.lastError',
      'Cannot read property of undefined',
      'chrome-extension://',
      'moz-extension://',
      'The message port closed before a response was received',
      'Unchecked runtime.lastError',
      'feedback.html'
    ];

    // Check both error message and event message
    const shouldSuppress = suppressedErrors.some(pattern =>
      message.toLowerCase().includes(pattern.toLowerCase()) ||
      (error && error.message && error.message.toLowerCase().includes(pattern.toLowerCase())) ||
      filename.toLowerCase().includes(pattern.toLowerCase())
    );

    if (shouldSuppress) {
      console.debug('[Extension Error Suppressed]:', message || error?.message);
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  }, true); // Use capture phase to catch errors early

  // Suppress unhandled promise rejections from extensions
  window.addEventListener('unhandledrejection', function(event) {
    const reason = event.reason;
    const message = reason?.message || reason?.toString() || '';

    const suppressedErrors = [
      'message port closed',
      'Extension context invalidated',
      'chrome-extension://',
      'moz-extension://',
      'runtime.lastError',
      'The message port closed before a response was received',
      'Unchecked runtime.lastError',
      'feedback.html'
    ];

    if (suppressedErrors.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()))) {
      console.debug('[Extension Promise Rejection Suppressed]:', message);
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  }, true); // Use capture phase

  console.debug('🔇 Browser extension error suppression initialized');
})();
