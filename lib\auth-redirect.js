/**
 * Authentication Redirect Helper
 * 
 * This module provides utilities for handling authentication redirects
 * in cross-origin environments.
 */

/**
 * Redirect to the login page with the current URL as the redirect target
 * @param {string} [currentPath] - The current path to redirect back to after login
 * @param {boolean} [useCrossOrigin=false] - Whether to use the cross-origin login page
 */
export function redirectToLogin(currentPath, useCrossOrigin = false) {
  if (typeof window === 'undefined') {
    return; // Server-side, can't redirect
  }
  
  // Store the current path in sessionStorage
  try {
    const redirectPath = currentPath || window.location.pathname;
    sessionStorage.setItem('redirect_after_login', redirectPath);
    console.log('Auth Redirect: Stored redirect path:', redirectPath);
  } catch (storageError) {
    console.warn('Error storing redirect path:', storageError);
  }
  
  // Determine which login page to use
  const loginPage = useCrossOrigin ? '/admin/cross-origin-login' : '/admin/login';
  
  // Redirect to the login page
  console.log('Auth Redirect: Redirecting to:', loginPage);
  window.location.href = loginPage;
}

/**
 * Check if we're in a cross-origin environment
 * @returns {boolean} True if we're in a cross-origin environment
 */
export function isCrossOriginEnvironment() {
  if (typeof window === 'undefined') {
    return false; // Server-side, not cross-origin
  }
  
  const isDev = process.env.NODE_ENV === 'development';
  const allowCrossOrigin = process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true';
  const devUrl = process.env.NEXT_PUBLIC_DEV_URL || '';
  
  // Check if we're in a cross-origin environment
  if (isDev && allowCrossOrigin && devUrl) {
    try {
      const devUrlObj = new URL(devUrl);
      const currentUrlObj = new URL(window.location.href);
      
      // Compare origins
      return devUrlObj.origin !== currentUrlObj.origin;
    } catch (urlError) {
      console.warn('Error parsing URLs:', urlError);
    }
  }
  
  return false;
}

/**
 * Redirect to the appropriate login page based on the environment
 * @param {string} [currentPath] - The current path to redirect back to after login
 */
export function redirectToAppropriateLogin(currentPath) {
  const useCrossOrigin = isCrossOriginEnvironment();
  redirectToLogin(currentPath, useCrossOrigin);
}

export default {
  redirectToLogin,
  isCrossOriginEnvironment,
  redirectToAppropriateLogin,
};
