/**
 * Development-only API endpoint for testing authentication
 * This endpoint provides a way to test authentication in development mode
 * without requiring a valid token.
 * 
 * IMPORTANT: This endpoint should never be used in production!
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';

export default async function handler(req, res) {
  // Only allow this endpoint in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'This endpoint is only available in development mode'
    });
  }

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Dev auth test request received`);

  // Get the token from the request
  const token = req.headers.authorization?.startsWith('Bearer ')
    ? req.headers.authorization.substring(7)
    : req.headers.authorization || req.query.token || 'dev-bypass-token';

  console.log(`[${requestId}] Using token: ${token.substring(0, 10)}...`);

  // Test authentication with the token
  try {
    // If using the dev bypass token, return success immediately
    if (token === 'dev-bypass-token') {
      console.log(`[${requestId}] Using development bypass token`);
      return res.status(200).json({
        success: true,
        message: 'Development bypass token accepted',
        user: {
          id: 'dev-user-id',
          email: '<EMAIL>'
        },
        role: 'admin',
        authorized: true,
        requestId
      });
    }

    // Otherwise, try normal authentication
    const authResult = await authenticateAdminRequest(req);
    const { authorized, error, user, role } = authResult;

    if (!authorized) {
      console.warn(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
      
      // Return detailed error information for diagnostics
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: error?.message || 'Unknown error',
        authorized: false,
        tokenFound: !!token,
        requestId
      });
    }

    // Authentication successful
    console.log(`[${requestId}] Authentication successful for user: ${user?.email}`);
    return res.status(200).json({
      success: true,
      message: 'Authentication successful',
      user: {
        id: user.id,
        email: user.email
      },
      role,
      authorized: true,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Error in dev auth test:`, error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
      requestId
    });
  }
}
