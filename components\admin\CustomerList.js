import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { authenticatedFetch } from '@/lib/auth-utils'
import LoadingButton from './LoadingButton'
import styles from '@/styles/admin/CustomerList.module.css'

export default function CustomerList() {
  const router = useRouter()
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [totalCustomers, setTotalCustomers] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [lastRefresh, setLastRefresh] = useState(Date.now())
  const [filters, setFilters] = useState({
    city: '',
    state: '',
    has_bookings: '',
    date_added: '',
    marketing_consent: ''
  })

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
    }, 500)

    return () => clearTimeout(timer)
  }, [search])

  // Reset page when search or filters change
  useEffect(() => {
    setPage(1)
  }, [debouncedSearch, filters])

  // Fetch customers when search, sort, filters, or page changes
  // Enhanced with better error handling, timeout mechanisms, and resource cleanup
  const fetchCustomers = useCallback(async () => {
    // Create an AbortController to cancel requests if component unmounts
    const controller = new AbortController();
    const signal = controller.signal;

    // Create a timeout ID for cleanup
    let timeoutId = null;

    // Track if the component is still mounted
    let isMounted = true;

    // Safe state update functions that check if component is mounted
    const safeSetLoading = (value) => {
      if (isMounted && !signal.aborted) {
        setLoading(value);
      }
    };

    const safeSetError = (value) => {
      if (isMounted && !signal.aborted) {
        setError(value);
      }
    };

    const safeSetCustomers = (value) => {
      if (isMounted && !signal.aborted) {
        setCustomers(value);
      }
    };

    const safeSetTotalCustomers = (value) => {
      if (isMounted && !signal.aborted) {
        setTotalCustomers(value);
      }
    };

    const safeSetTotalPages = (value) => {
      if (isMounted && !signal.aborted) {
        setTotalPages(value);
      }
    };

    safeSetLoading(true);
    safeSetError(null);

    try {
      const offset = (page - 1) * limit;
      const queryParams = new URLSearchParams({
        limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Create an AbortController for the authenticatedFetch
      const abortController = new AbortController();

      // Set up a timeout to abort the request if it takes too long
      timeoutId = setTimeout(() => {
        abortController.abort();
        if (isMounted && !signal.aborted) {
          safeSetError('Request timed out. Please try again.');
          safeSetLoading(false);
        }
      }, 15000); // 15 second timeout

      // Use authenticatedFetch with the signal from both controllers
      const data = await authenticatedFetch(
        `/api/admin/customers?${queryParams.toString()}`,
        {
          signal: abortController.signal,
          // Don't redirect on auth failure during list view to prevent disruption
          // Just show the error message instead
        },
        {
          redirect: false,
          notify: true
        }
      );

      // Clear the timeout since the request completed
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (signal.aborted || !isMounted) {
        // Request was aborted or component unmounted, don't update state
        return;
      }

      // The API returns an object with customers property for compatibility
      if (!data) {
        console.error('Invalid response format: no data received');
        throw new Error('Invalid response from server');
      }

      // Handle both array format (legacy) and object format (current)
      let customers, totalCount;

      if (Array.isArray(data)) {
        // Legacy format: direct array
        customers = data;
        totalCount = data.length;
      } else if (data.customers && Array.isArray(data.customers)) {
        // Current format: object with customers property
        customers = data.customers;
        totalCount = data.total || data.customers.length;
      } else {
        console.error('Invalid response format:', typeof data, data);
        throw new Error('Invalid response from server');
      }

      // Set the customers from the processed data
      safeSetCustomers(customers);
      safeSetTotalCustomers(totalCount);
      safeSetTotalPages(Math.ceil(totalCount / limit));
    } catch (error) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (signal.aborted || !isMounted) {
        // Request was aborted or component unmounted, don't update state
        return;
      }

      console.error('Error fetching customers:', error);
      safeSetError(error.message || 'Failed to load customers');

      // Set empty data to prevent UI from hanging
      safeSetCustomers([]);
      safeSetTotalCustomers(0);
      safeSetTotalPages(0);
    } finally {
      if (!signal.aborted && isMounted) {
        safeSetLoading(false);
      }
    }

    // Return cleanup function
    return () => {
      isMounted = false;

      // Abort the fetch if component unmounts
      if (!signal.aborted) {
        controller.abort();
      }

      // Clear any remaining timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [page, limit, sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    fetchCustomers()
  }, [fetchCustomers])

  // Set up auto-refresh for customer data (every 60 seconds)
  // Enhanced with better error handling and resource management
  useEffect(() => {
    // Track if component is mounted
    let isMounted = true;

    // Create a function to safely refresh data
    const refreshData = async () => {
      if (!isMounted) return;

      // Don't auto-refresh if there's an error or if we're already loading
      if (loading || error) return;

      try {
        // Update last refresh timestamp
        setLastRefresh(Date.now());

        // Create a timeout for the refresh operation
        const refreshPromise = fetchCustomers();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auto-refresh timeout')), 10000)
        );

        // Race the refresh against the timeout
        await Promise.race([refreshPromise, timeoutPromise]);
      } catch (refreshError) {
        console.error('Auto-refresh failed:', refreshError);
        // Don't set error state to prevent UI disruption during auto-refresh
      }
    };

    // Start auto-refresh with a longer interval (60 seconds instead of 30)
    // to reduce server load and prevent potential race conditions
    const interval = setInterval(refreshData, 60000); // 60 seconds

    // Clean up on unmount
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [fetchCustomers, loading, error]);

  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // Default to ascending order for new column
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    )
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Check if a date is within the last 7 days
  const isRecent = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const sevenDaysAgo = new Date(now.setDate(now.getDate() - 7))
    return date >= sevenDaysAgo
  }

  // Handle export
  const handleExport = (format = 'csv', options = {}) => {
    const queryParams = new URLSearchParams({
      format,
      ...options
    })

    // Add current filters to export
    if (debouncedSearch) {
      queryParams.append('search', debouncedSearch)
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        queryParams.append(key, value)
      }
    })

    window.open(`/api/customers/export?${queryParams.toString()}`, '_blank')
  }

  return (
    <div className={styles.customerList}>
      <div className={styles.header}>
        <h2>Customers</h2>
        <div className={styles.actions}>
          <button
            className={styles.addButton}
            onClick={() => router.push('/admin/customers/new')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Customer
          </button>
          <div className={styles.exportDropdown}>
            <button className={styles.exportButton}>
              Export
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className={styles.exportOptions}>
              <button onClick={() => handleExport('csv')}>Current View (CSV)</button>
              <button onClick={() => handleExport('csv', { marketing_only: true })}>Marketing Opt-in Only (CSV)</button>
              <button onClick={() => handleExport('csv', { include_bookings: true })}>Include Booking History (CSV)</button>
              <button onClick={() => handleExport('excel')}>Current View (Excel)</button>
              <button onClick={() => handleExport('json')}>Current View (JSON)</button>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.filters}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Search by name, email, or phone..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={styles.searchIcon}>
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </div>

        <div className={styles.filterControls}>
          <select
            name="city"
            value={filters.city}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Cities</option>
            <option value="Sydney">Sydney</option>
            <option value="Melbourne">Melbourne</option>
            <option value="Brisbane">Brisbane</option>
            <option value="Perth">Perth</option>
            <option value="Adelaide">Adelaide</option>
          </select>

          <select
            name="state"
            value={filters.state}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All States</option>
            <option value="NSW">NSW</option>
            <option value="VIC">VIC</option>
            <option value="QLD">QLD</option>
            <option value="WA">WA</option>
            <option value="SA">SA</option>
            <option value="TAS">TAS</option>
            <option value="ACT">ACT</option>
            <option value="NT">NT</option>
          </select>

          <select
            name="has_bookings"
            value={filters.has_bookings}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Booking Status</option>
            <option value="true">Has Bookings</option>
            <option value="false">No Bookings</option>
          </select>

          <select
            name="date_added"
            value={filters.date_added}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Dates</option>
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">Last Year</option>
          </select>

          <select
            name="marketing_consent"
            value={filters.marketing_consent}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Marketing Status</option>
            <option value="true">Opted In</option>
            <option value="false">Opted Out</option>
          </select>
        </div>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {loading ? (
        <div className={styles.loading}>Loading customers...</div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.customerTable}>
              <thead>
                <tr>
                  <th onClick={() => handleSort('name')}>
                    Name {renderSortIndicator('name')}
                  </th>
                  <th onClick={() => handleSort('email')}>
                    Email {renderSortIndicator('email')}
                  </th>
                  <th onClick={() => handleSort('phone')}>
                    Phone {renderSortIndicator('phone')}
                  </th>
                  <th onClick={() => handleSort('city')}>
                    Location {renderSortIndicator('city')}
                  </th>
                  <th onClick={() => handleSort('created_at')}>
                    Created {renderSortIndicator('created_at')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer) => (
                  <tr key={customer.id} className={customer.recent_booking ? styles.recentBooking : ''}>
                    <td>
                      {customer.name}
                      {customer.marketing_consent && (
                        <span className={styles.marketingBadge} title="Marketing Opt-in">📧</span>
                      )}
                    </td>
                    <td>{customer.email}</td>
                    <td>{customer.phone || '-'}</td>
                    <td>
                      {customer.city ? `${customer.city}, ${customer.state || ''}` : '-'}
                    </td>
                    <td>
                      {new Date(customer.created_at).toLocaleDateString()}
                      {isRecent(customer.created_at) && (
                        <span className={styles.newBadge} title="New Customer">New</span>
                      )}
                    </td>
                    <td className={styles.actions}>
                      <Link
                        href={`/admin/customers/${customer.id}`}
                        className={styles.viewButton}
                      >
                        View
                      </Link>
                      <Link
                        href={`/admin/customers/${customer.id}/edit`}
                        className={styles.editButton}
                      >
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {customers.length === 0 && (
            <div className={styles.noResults}>No customers found</div>
          )}

          <div className={styles.pagination}>
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={styles.paginationButton}
            >
              Previous
            </button>
            <span className={styles.pageInfo}>
              Page {page} of {totalPages} ({totalCustomers} customers)
            </span>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages || totalPages === 0}
              className={styles.paginationButton}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  )
}
