#!/usr/bin/env node

/**
 * Setup Invoices Table for Migration
 * 
 * This script ensures the invoices table has the correct structure
 * for the enhanced invoice import process.
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupInvoicesTable() {
  console.log('🔧 Setting up Invoices Table for Migration...\n');
  
  try {
    // First, let's check the current table structure
    console.log('📋 Checking current table structure...');
    
    // Try to describe the table structure using a query
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('get_table_columns', { table_name: 'invoices' })
      .single();

    if (tableError) {
      console.log('Using alternative method to check table...');
    }

    // Add missing columns for migration tracking
    console.log('🔧 Adding migration-specific columns...');
    
    const alterQueries = [
      // Add columns for migration tracking if they don't exist
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS customer_name_original TEXT`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS match_type TEXT`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS match_confidence DECIMAL(3,2)`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'wix_migration'`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS order_number TEXT`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10,2)`,
      
      `ALTER TABLE public.invoices 
       ADD COLUMN IF NOT EXISTS discount DECIMAL(10,2) DEFAULT 0`,
      
      // Make order_id and booking_id nullable for migration
      `ALTER TABLE public.invoices 
       ALTER COLUMN order_id DROP NOT NULL`,
      
      `ALTER TABLE public.invoices 
       ALTER COLUMN booking_id DROP NOT NULL`
    ];

    for (const query of alterQueries) {
      try {
        const { error } = await supabase.rpc('execute_sql', { sql: query });
        if (error && !error.message.includes('already exists')) {
          console.log(`⚠️ Query warning: ${error.message}`);
        }
      } catch (e) {
        console.log(`⚠️ Query note: ${e.message}`);
      }
    }

    // Test the table structure with a sample insert
    console.log('🧪 Testing table structure...');
    
    const testRecord = {
      invoice_number: 'MIGRATION_TEST_' + Date.now(),
      customer_id: null, // This should be allowed for migration
      amount: 100.00,
      currency: 'AUD',
      issue_date: new Date().toISOString(),
      due_date: new Date().toISOString(),
      status: 'draft',
      customer_name_original: 'Test Customer',
      match_type: 'test',
      match_confidence: 0.95,
      source: 'migration_test'
    };

    // Try to insert test record
    const { data: insertData, error: insertError } = await supabase
      .from('invoices')
      .insert([testRecord])
      .select();

    if (insertError) {
      console.log('❌ Test insert failed:', insertError.message);
      
      // If it's a constraint error, we need to handle it
      if (insertError.message.includes('check constraint')) {
        console.log('🔧 Handling check constraint...');
        
        // The constraint likely requires either order_id or booking_id
        // Let's modify our approach for migration
        console.log('📝 Migration will need to handle constraint requirements');
      }
      
      return false;
    } else {
      console.log('✅ Test insert successful');
      
      // Clean up test record
      if (insertData && insertData[0]) {
        await supabase
          .from('invoices')
          .delete()
          .eq('id', insertData[0].id);
        console.log('🧹 Cleaned up test record');
      }
    }

    // Create indexes for better performance
    console.log('📊 Creating performance indexes...');
    
    const indexQueries = [
      `CREATE INDEX IF NOT EXISTS idx_invoices_customer_name_original 
       ON public.invoices(customer_name_original)`,
      
      `CREATE INDEX IF NOT EXISTS idx_invoices_match_type 
       ON public.invoices(match_type)`,
      
      `CREATE INDEX IF NOT EXISTS idx_invoices_source 
       ON public.invoices(source)`
    ];

    for (const query of indexQueries) {
      try {
        const { error } = await supabase.rpc('execute_sql', { sql: query });
        if (error) {
          console.log(`⚠️ Index creation note: ${error.message}`);
        }
      } catch (e) {
        console.log(`⚠️ Index note: ${e.message}`);
      }
    }

    console.log('\n✅ INVOICES TABLE SETUP COMPLETED');
    console.log('==================================');
    console.log('✅ Migration-specific columns added');
    console.log('✅ Performance indexes created');
    console.log('✅ Table ready for enhanced invoice import');
    
    return true;

  } catch (error) {
    console.error('❌ Failed to setup invoices table:', error.message);
    return false;
  }
}

// Run setup
if (require.main === module) {
  setupInvoicesTable()
    .then(success => {
      if (success) {
        console.log('\n🎯 Ready to proceed with invoice import!');
      } else {
        console.log('\n❌ Setup failed - manual intervention may be required');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Setup script failed:', error);
      process.exit(1);
    });
}

module.exports = { setupInvoicesTable };
