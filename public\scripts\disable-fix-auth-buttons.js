/**
 * Disable Fix Auth Buttons Script
 * 
 * This script removes or hides the development "Fix Auth" buttons that appear
 * in the browser to provide a cleaner user experience in production.
 */

(function() {
  'use strict';

  console.log('[Fix Auth Cleanup] Starting cleanup of development auth buttons...');

  // Function to remove fix auth buttons
  function removeFixAuthButtons() {
    // Remove buttons by ID
    const buttonIds = [
      'auth-recovery-btn',
      'fix-auth-button',
      'manual-auth-recovery'
    ];

    buttonIds.forEach(id => {
      const button = document.getElementById(id);
      if (button) {
        console.log(`[Fix Auth Cleanup] Removing button with ID: ${id}`);
        button.remove();
      }
    });

    // Remove buttons by text content
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      const text = button.textContent.trim().toLowerCase();
      if (text.includes('fix auth') || text.includes('🔧')) {
        console.log(`[Fix Auth Cleanup] Removing button with text: ${button.textContent}`);
        button.remove();
      }
    });

    // Remove any floating fix auth elements
    const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
    fixedElements.forEach(element => {
      const text = element.textContent.toLowerCase();
      if (text.includes('fix auth') || text.includes('🔧')) {
        console.log(`[Fix Auth Cleanup] Removing fixed element: ${element.textContent}`);
        element.remove();
      }
    });
  }

  // Run cleanup immediately
  removeFixAuthButtons();

  // Run cleanup when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', removeFixAuthButtons);
  }

  // Run cleanup periodically to catch dynamically added buttons
  setInterval(removeFixAuthButtons, 2000);

  // Also run when the page becomes visible (tab switching)
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      setTimeout(removeFixAuthButtons, 100);
    }
  });

  console.log('[Fix Auth Cleanup] Cleanup script initialized');
})();
