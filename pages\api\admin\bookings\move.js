import { createClient } from '@supabase/supabase-js';
import { isValidStatusTransition } from '@/lib/booking-status';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * API endpoint for moving bookings (changing time/date)
 * 
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  // Generate a request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 15);
  
  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/bookings/move`);
  
  // Only allow PUT method
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    // Get booking ID and new times from request body
    const { id, start_time, end_time, status } = req.body;
    
    // Validate required fields
    if (!id || !start_time || !end_time) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get the booking to check for conflicts
    const { data: booking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError) {
      console.error(`[${requestId}] Error fetching booking:`, fetchError);
      return res.status(404).json({ error: 'Booking not found' });
    }
    
    // Check for conflicts with other bookings
    const { data: conflicts, error: conflictError } = await supabase
      .from('bookings')
      .select('id, customer_id, service_id, start_time, end_time, status, location, customers(name, email, phone)')
      .neq('id', id) // Exclude the current booking
      .neq('status', 'canceled') // Exclude canceled bookings
      .eq('location', booking.location) // Same location
      .or(`start_time.lt.${end_time},end_time.gt.${start_time}`); // Time overlap
      
    if (conflictError) {
      console.error(`[${requestId}] Error checking conflicts:`, conflictError);
      return res.status(500).json({ error: 'Error checking for conflicts' });
    }
    
    // If conflicts found, return error
    if (conflicts && conflicts.length > 0) {
      return res.status(409).json({ 
        error: 'Booking conflicts with existing bookings',
        conflicts
      });
    }
    
    // Prepare update data
    const updateData = {
      start_time,
      end_time,
      updated_at: new Date().toISOString()
    };
    
    // If status is provided and different from current, update it
    if (status && status !== booking.status) {
      // Validate status transition
      if (!isValidStatusTransition(booking.status, status)) {
        return res.status(400).json({ 
          error: `Invalid status transition from ${booking.status} to ${status}` 
        });
      }
      
      updateData.status = status;
    }
    
    // Update the booking
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', id)
      .select();
      
    if (updateError) {
      console.error(`[${requestId}] Error updating booking:`, updateError);
      return res.status(500).json({ error: 'Error updating booking' });
    }
    
    // If status was changed, record in status history
    if (status && status !== booking.status) {
      const { error: historyError } = await supabase
        .from('booking_status_history')
        .insert([{
          booking_id: id,
          previous_status: booking.status,
          new_status: status,
          notes: 'Status changed during booking move'
        }]);
        
      if (historyError) {
        console.error(`[${requestId}] Error recording status history:`, historyError);
        // Don't fail the request, just log the error
      }
    }
    
    // Return the updated booking
    return res.status(200).json({ booking: updatedBooking[0] });
  } catch (error) {
    console.error(`[${requestId}] Unhandled error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
