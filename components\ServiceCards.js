import { useState } from 'react';
import Link from 'next/link';
import styles from '@/styles/ServiceCards.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * Interactive ServiceCards component with flip animations and modern design
 *
 * @param {Object} props - Component props
 * @param {Array} props.services - Array of service objects with details
 * @returns {JSX.Element}
 */
const ServiceCards = ({
  services = [],
  ...props
}) => {
  const [activeCategory, setActiveCategory] = useState('all');

  // Default services if none provided
  const defaultServices = [
    {
      id: 'airbrush',
      title: 'Airbrush Face & Body Painting',
      description: 'Add flair and colour to any event with our airbrush face and body painting. From intricate designs to bold statements, we tailor our artistry to suit your theme or outfit.',
      image: '/images/services/airbrush-painting.jpeg',
      category: 'painting',
      pricing: [
        { title: 'Individual designs', price: 'from $15' },
        { title: 'Group bookings (10+ people)', price: 'from $12 per person' },
        { title: 'Event packages', price: 'Please contact for custom quotes' }
      ],
      accentColor: '#4ECDC4'
    },
    {
      id: 'braiding',
      title: 'Braiding',
      description: 'Transform your look with our vibrant braiding services, including festival-ready styles and coloured extensions to match your vibe. We offer single bookings, braid parties, and festival services.',
      image: '/images/services/festival-braids.jpg',
      category: 'hair',
      pricing: [
        { title: 'Basic braids', price: 'from $60' },
        { title: 'Colored extensions', price: 'from $15 per color' },
        { title: 'Full head braiding', price: 'from $120' },
        { title: 'Braid parties (5+ people)', price: 'Please contact for group rates' }
      ],
      accentColor: '#FF6B6B'
    },
    {
      id: 'face-painting',
      title: 'Face Painting for Kids',
      description: 'Bring magical moments to life with our kids\' face painting service, featuring enchanting designs like rainbows, unicorns, and superheroes. Ideal for children under 12.',
      image: '/images/services/face-paint.jpg',
      category: 'painting',
      pricing: [
        { title: 'Basic designs', price: 'from $10' },
        { title: 'Premium designs', price: 'from $15' },
        { title: 'Party packages (2 hours)', price: 'from $200' },
        { title: 'Additional hour', price: '$80' }
      ],
      accentColor: '#FFE66D'
    },
    {
      id: 'glitter',
      title: 'Glitter & Sparkle Art',
      description: 'Elevate your look with dazzling glitter designs and biodegradable sparkles, perfect for festivals and parties. Our eco-friendly products ensure you shine while caring for the environment.',
      image: '/Glitter Bar.png',
      category: 'sparkle',
      pricing: [
        { title: 'Festival glitter', price: 'from $20' },
        { title: 'Face gems application', price: 'from $15' },
        { title: 'Full body sparkle', price: 'from $45' }
      ],
      accentColor: '#1A73E8'
    }
  ];

  const displayServices = services.length > 0 ? services : defaultServices;

  // Filter services based on active category
  const filteredServices = activeCategory === 'all'
    ? displayServices
    : displayServices.filter(service => service.category === activeCategory);

  // Get unique categories for filter
  const categories = ['all', ...new Set(displayServices.map(service => service.category))];

  return (
    <section className={styles.servicesSection} {...props}>
      <div className={styles.categoryFilters}>
        {categories.map((category, index) => (
          <button
            key={index}
            className={`${styles.categoryButton} ${activeCategory === category ? styles.activeCategory : ''}`}
            onClick={() => setActiveCategory(category)}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>

      <div className={styles.serviceCardsGrid}>
        {filteredServices.map((service, index) => (
          <AnimatedSection
            key={service.id}
            animation="fade-in"
            delay={100 * index}
            className={styles.cardWrapper}
          >
            <div className={styles.serviceCard}>
              <div className={styles.cardInner}>
                {/* Front of card */}
                <div className={styles.cardFront}>
                  <div
                    className={styles.cardImage}
                    style={{ backgroundImage: `url(${service.image})` }}
                  >
                    <div
                      className={styles.imageOverlay}
                      style={{ background: `linear-gradient(to bottom, transparent, ${service.accentColor})` }}
                    ></div>
                  </div>
                  <div className={styles.cardContent}>
                    <h3 className={styles.cardTitle}>{service.title}</h3>
                    <p className={styles.cardDescription}>{service.description}</p>
                    <div className={styles.flipPrompt}>
                      <span>Click/tap for pricing</span>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 2L17 12L7 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Back of card */}
                <div
                  className={styles.cardBack}
                  style={{ backgroundColor: service.accentColor }}
                >
                  <div className={styles.cardBackContent}>
                    <h3 className={styles.pricingTitle}>Pricing</h3>
                    <ul className={styles.pricingList}>
                      {service.pricing.map((item, idx) => (
                        <li key={idx} className={styles.pricingItem}>
                          <span className={styles.pricingItemTitle}>{item.title}</span>
                          <span className={styles.pricingItemPrice}>{item.price}</span>
                        </li>
                      ))}
                    </ul>
                    <Link href="/book-online" className={styles.bookButton}>
                      Book Now
                    </Link>
                    <div className={styles.flipBack}>
                      <span>Back to details</span>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 22L7 12L17 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedSection>
        ))}
      </div>
    </section>
  );
};

export default ServiceCards;