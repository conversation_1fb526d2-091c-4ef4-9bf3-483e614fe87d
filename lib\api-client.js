/**
 * Ocean Soul Sparkles API Client
 *
 * Centralized utility for making API requests to the admin backend
 * with proper authentication headers
 */
import supabase from './supabase';
import { StandardError, NetworkError, AuthenticationError, AuthorizationError, NotFoundError, ConflictError, RateLimitError, ServerError, ServiceUnavailableError, PaymentError, InvalidInputError, OperationFailedError, ExternalServiceError, ConfigurationError, DatabaseError, FileSystemError, TimeoutError, PermissionDeniedError, ResourceLockedError, QuotaExceededError, InvalidStateError, ConcurrencyError, MaintenanceError, DeprecatedError, UserCancelledError, UnknownError } from './errors';

class ApiClient {
  static instance = null;

  /**
   * Get singleton instance
   */
  static getInstance() {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  constructor() {
    this.token = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
    this.isDevMode = process.env.NODE_ENV !== 'production';

    // Detect browser for optimizations
    this.isChrome = false;
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent;
      this.isChrome = userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') === -1;
    }

    // Log environment info for debugging
    if (typeof window !== 'undefined' && this.isDevMode) {
      console.log(`API Client initialized in development mode${this.isChrome ? ' (Chrome detected)' : ''}`);
    }
  }
    /**
   * Make an authenticated request to the API
   * Enhanced for better auth handling, memory management, and error recovery
   * Improved to handle authentication failures more gracefully
   *
   * @param {string} endpoint - API endpoint path
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - Response data
   */
  async request(endpoint, options = {}) {
    // Track retry attempts to prevent infinite loops
    const retryCount = options.__retryCount || 0;
    const retries = options.retries || 2;
    const retryDelay = options.retryDelay || 1000;

    // Prevent excessive retries
    if (retryCount > retries) {
      throw new Error(`Maximum retry attempts (${retries}) exceeded for ${endpoint}`);
    }

    // Check if we're in a diagnostics context
    const isDiagnostics = endpoint.includes('/api/admin/diagnostics') ||
                         options.__isDiagnostics ||
                         (typeof window !== 'undefined' &&
                          window.location.pathname.includes('/admin/diagnostics'));

    // For diagnostics requests, use special options
    if (isDiagnostics && !options.__isDiagnostics) {
      options = {
        ...options,
        __isDiagnostics: true,
        __noRedirect: true, // Don't redirect on auth failures
        retries: 1,         // Fewer retries
        timeout: 5000       // Shorter timeout
      };
    }

    // Create an AbortController for this request if not provided
    let controllerOwned = false;
    let controller;

    if (options.signal) {
      // Use the provided signal
      controller = { signal: options.signal };
    } else {
      // Create our own controller
      controller = new AbortController();
      controllerOwned = true;
    }

    // Set a timeout to prevent hanging requests
    // Use a shorter timeout for Chrome to prevent unresponsiveness
    let timeoutId = null;

    if (controllerOwned) {
      const timeoutDuration = this.isChrome ?
        (options.timeout || 10000) : // 10 second default for Chrome
        (options.timeout || 15000);  // 15 second default for other browsers

      timeoutId = setTimeout(() => {
        try {
          controller.abort();
        } catch (abortError) {
          console.error('Error aborting request:', abortError);
        }
      }, timeoutDuration);
    }

    try {
      // Generate a shorter unique request ID for tracking (reduces memory usage)
      const requestId = Math.random().toString(36).substring(2, 10);

      // Ensure we have a valid token with timeout and better error handling
      let token = null;
      let tokenError = null;

      try {
        // Only get token if we're not already in a retry loop for token refresh
        if (!options.__tokenRefreshRetry) {
          // Set a timeout for token fetch
          let tokenTimeoutId = null;
          const tokenTimeoutPromise = new Promise((_, reject) => {
            tokenTimeoutId = setTimeout(() => {
              reject(new Error('Token fetch timeout'));
            }, 5000);
          });

          try {
            // Get token with timeout
            const tokenPromise = this.getAuthToken();
            token = await Promise.race([tokenPromise, tokenTimeoutPromise]);

            // Clear the timeout since we got a response
            if (tokenTimeoutId) {
              clearTimeout(tokenTimeoutId);
              tokenTimeoutId = null;
            }

            // If token is null but no error was thrown, create a specific error
            if (token === null) {
              throw new Error('Authentication failed: No valid token available');
            }
          } catch (tokenError) {
            // Clear the timeout to prevent memory leaks
            if (tokenTimeoutId) {
              clearTimeout(tokenTimeoutId);
            }

            throw tokenError;
          }
        }
      } catch (error) {
        console.error(`[${requestId}] Failed to get auth token:`, error);
        tokenError = error;

        // Check if we need to redirect to login
        if (typeof window !== 'undefined' &&
            !options.__noRedirect &&
            error.message &&
            (error.message.includes('Authentication failed') ||
             error.message.includes('No active session'))) {

          console.warn(`[${requestId}] Authentication failure detected, redirecting to login`);          // Add a small delay to allow logs to be sent
          setTimeout(() => {
            try {
              // Clear any invalid tokens
              // Clear all possible token storage locations
              if (typeof window.sessionStorage !== 'undefined') {
                sessionStorage.removeItem('oss_auth_token_cache');
                sessionStorage.removeItem('oss_session');
              }

              if (typeof window.localStorage !== 'undefined') {
                localStorage.removeItem('oss_auth_token');
                localStorage.removeItem('sb_auth_token');
              }

              // Clear cookies via document.cookie
              document.cookie = 'oss_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
              document.cookie = 'sb_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

              // Redirect to login page
              window.location.href = '/admin/login?redirect=' + encodeURIComponent(window.location.pathname);
            } catch (redirectError) {
              console.error(`[${requestId}] Error redirecting to login:`, redirectError);
            }
          }, 100);
        }

        // Continue without token, the request will likely fail with 401
      }

      // Add auth header to request
      const headers = {
        ...options.headers,
        'Content-Type': 'application/json',
      };

      // Only add Authorization header if we have a token
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Add environment-specific headers for debugging
      headers['X-Client-Info'] = this.isDevMode ? 'api-client/dev' : 'api-client/prod';

      // Add request ID for tracking in logs
      headers['X-Request-ID'] = requestId;

      // Log requests in dev mode to help with debugging
      if (this.isDevMode) {
        console.log(`API Request [${requestId}]: ${endpoint} (retry: ${retryCount})`);

        if (tokenError) {
          console.warn(`[${requestId}] Proceeding without auth token due to error:`, tokenError.message);
        }
      }

      // Make the request with credentials for cookie handling
      const fetchOptions = {
        ...options,
        headers,
        credentials: 'include', // Needed for cookie-based auth
        signal: controller.signal,
      };

      // Remove internal options that shouldn't be passed to fetch
      delete fetchOptions.__retryCount;
      delete fetchOptions.__tokenRefreshRetry;
      delete fetchOptions.retries;
      delete fetchOptions.retryDelay;

      const response = await fetch(endpoint, fetchOptions);

      // Debug response in dev mode
      if (this.isDevMode && response.status !== 200) {
        console.warn(`API response status [${requestId}]: ${response.status}`);
      }

      // Handle unauthorized errors by refreshing the token and retrying once
      if (response.status === 401 && retryCount < 1 && !options.__tokenRefreshRetry) {
        // Force token refresh and retry
        try {
          // Clear the timeout since we're going to retry
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          // Log the retry attempt
          if (this.isDevMode) {
            console.log(`[${requestId}] Unauthorized response, attempting token refresh and retry`);
          }

          // Force clear any cached tokens
          this.token = null;
          this.tokenExpiry = null;

          if (typeof window !== 'undefined') {
            try {
              sessionStorage.removeItem('oss_auth_token_cache');
            } catch (storageError) {
              console.warn(`[${requestId}] Error clearing token storage:`, storageError);
            }
          }

          // Get a fresh token
          let newToken = null;
          try {
            // Set a timeout for token refresh
            let refreshTimeoutId = null;
            const refreshTimeoutPromise = new Promise((_, reject) => {
              refreshTimeoutId = setTimeout(() => {
                reject(new Error('Token refresh timeout'));
              }, 5000);
            });

            try {
              // Force a fresh token fetch
              const refreshPromise = this.refreshAuthToken();
              newToken = await Promise.race([refreshPromise, refreshTimeoutPromise]);

              // Clear the timeout since we got a response
              if (refreshTimeoutId) {
                clearTimeout(refreshTimeoutId);
                refreshTimeoutId = null;
              }
            } catch (refreshError) {
              // Clear the timeout to prevent memory leaks
              if (refreshTimeoutId) {
                clearTimeout(refreshTimeoutId);
              }

              throw refreshError;
            }
          } catch (tokenError) {
            console.error(`[${requestId}] Failed to refresh token after 401:`, tokenError);

            // Check if we need to redirect to login
            if (typeof window !== 'undefined' && !options.__noRedirect) {
              console.warn(`[${requestId}] Authentication failure after 401, redirecting to login`);

              // Add a small delay to allow logs to be sent
              setTimeout(() => {
                try {                  // Clear all possible token storage locations
                  if (typeof window.sessionStorage !== 'undefined') {
                    sessionStorage.removeItem('oss_auth_token_cache');
                    sessionStorage.removeItem('oss_session');
                  }if (typeof window.localStorage !== 'undefined') {
                    localStorage.removeItem('oss_auth_token');
                    localStorage.removeItem('sb_auth_token');
                  }

                  // Clear cookies via document.cookie
                  document.cookie = 'oss_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                  document.cookie = 'sb_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                  // Redirect to login page
                  window.location.href = '/admin/login?redirect=' + encodeURIComponent(window.location.pathname);
                } catch (redirectError) {
                  console.error(`[${requestId}] Error redirecting to login:`, redirectError);
                }
              }, 100);
            }

            throw new Error('Authentication failed: Unable to refresh token');
          }

          if (!newToken) {
            throw new Error('Authentication failed: Token refresh returned null');
          }

          // Retry the request with token refresh flag to prevent infinite loops
          return this.request(endpoint, {
            ...options,
            __retryCount: retryCount + 1,
            __tokenRefreshRetry: true
          });
        } catch (refreshError) {
          console.error(`[${requestId}] Token refresh retry failed:`, refreshError);
          throw new Error('Authentication failed: Unable to refresh token');
        }
      }

      // Parse the response
      let data;
      try {
        // Only try to parse JSON if there's content
        if (response.status !== 204) {
          const text = await response.text();
          data = text ? JSON.parse(text) : {};
        } else {
          data = { success: true }; // No content but success
        }
      } catch (parseError) {
        console.error(`API response parsing failed [${requestId}]:`, parseError);
        // Return a structured error for better debugging
        throw new Error(`API response parsing failed: ${parseError.message}`);
      }

      // Handle error responses
      if (!response.ok) {
        const errorMessage = data?.error || data?.message || `API error: ${response.status}`;
        console.error(`API error [${requestId}]:`, errorMessage);

        // For server errors (5xx), retry the request
        if (response.status >= 500 && retryCount < retries) {
          // Clear the timeout since we're going to retry
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          // Log the retry attempt
          if (this.isDevMode) {
            console.log(`[${requestId}] Server error (${response.status}), retrying in ${retryDelay}ms`);
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay * (retryCount + 1)));

          // Retry the request
          return this.request(endpoint, {
            ...options,
            __retryCount: retryCount + 1
          });
        }

        throw new Error(errorMessage);
      }

      // Clear the timeout since request completed successfully
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      return data;
    } catch (error) {
      // Clear the timeout to prevent memory leaks
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Check if request was aborted
      if (error.name === 'AbortError') {
        throw new Error(`Request to ${endpoint} timed out`);
      }

      // For network errors, retry the request
      if (error.message && (
          error.message.includes('network') ||
          error.message.includes('connection') ||
          error.message.includes('timeout')
        ) && retryCount < retries) {

        // Log the retry attempt
        if (this.isDevMode) {
          console.log(`Network error for ${endpoint}, retrying in ${retryDelay}ms:`, error.message);
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay * (retryCount + 1)));

        // Retry the request
        return this.request(endpoint, {
          ...options,
          __retryCount: retryCount + 1
        });
      }

      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }
    /**
   * Get current auth token using a simplified approach
   * Uses a single, consistent method for token retrieval
   *
   * @returns {Promise<string|null>} - JWT token
   */
  async getAuthToken() {
    const now = Date.now();
    const requestId = Math.random().toString(36).substring(2, 8);

    // Check for token in memory first
    if (this.token && this.tokenExpiry && this.tokenExpiry > now) {
      if (this.isDevMode) {
        console.log(`[${requestId}] Using valid in-memory token (expires in ${Math.round((this.tokenExpiry - now) / 1000)}s)`);
      }
      return this.token;
    }

    // Check for token in sessionStorage
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
        if (cachedToken) {
          try {
            const tokenData = JSON.parse(cachedToken);
            if (tokenData && tokenData.token && tokenData.expiry > now) {
              // Store in memory for future use
              this.token = tokenData.token;
              this.tokenExpiry = tokenData.expiry;

              if (this.isDevMode) {
                console.log(`[${requestId}] Using token from sessionStorage (expires in ${Math.round((tokenData.expiry - now) / 1000)}s)`);
              }
              return this.token;
            } else {
              if (this.isDevMode) {
                console.log(`[${requestId}] Token in sessionStorage is expired or invalid`);
              }
              // Remove invalid token from sessionStorage
              sessionStorage.removeItem('oss_auth_token_cache');
            }
          } catch (parseError) {
            console.warn(`[${requestId}] Error parsing token from sessionStorage:`, parseError);
            // Remove invalid token from sessionStorage
            sessionStorage.removeItem('oss_auth_token_cache');
          }
        }
      } catch (storageError) {
        console.warn(`[${requestId}] Error accessing sessionStorage:`, storageError);
      }
    }

    if (this.isDevMode) {
      console.log(`[${requestId}] Token expired or not available, fetching new token`);
    }

    // Fetch a new token
    try {
      return await this.refreshAuthToken();
    } catch (refreshError) {
      console.error(`[${requestId}] Failed to refresh auth token:`, refreshError);

      // Clear any invalid tokens
      this.token = null;
      this.tokenExpiry = null;

      // Clear any stored tokens to ensure a clean state
      if (typeof window !== 'undefined') {
        try {          // Clear sessionStorage
          if (window.sessionStorage) {
            sessionStorage.removeItem('oss_auth_token_cache');
            sessionStorage.removeItem('oss_session');
          }

          // We're standardizing on not using localStorage for tokens
          if (window.localStorage) {
            localStorage.removeItem('oss_auth_token');
            localStorage.removeItem('sb_auth_token');
          }
        } catch (clearError) {
          console.warn(`[${requestId}] Error clearing token storage:`, clearError);
        }
      }

      // Return null to indicate authentication failure
      return null;
    }
  }

  /**
   * Refresh the auth token from Supabase using a simplified approach
   *
   * @returns {Promise<string|null>} - New JWT token
   */
  async refreshAuthToken() {
    // Generate a unique refresh ID for tracking
    const refreshId = Math.random().toString(36).substring(2, 8);

    // If there's already a refresh in progress, wait for it
    if (this.refreshPromise) {
      try {
        if (this.isDevMode) {
          console.log(`[${refreshId}] Token refresh already in progress, waiting...`);
        }
        return await this.refreshPromise;
      } catch (error) {
        console.error(`[${refreshId}] Error waiting for token refresh:`, error);
        this.refreshPromise = null;
      }
    }

    if (this.isDevMode) {
      console.log(`[${refreshId}] Starting token refresh`);
    }

    // Create a timeout to prevent hanging indefinitely
    let timeoutId = null;
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        reject(new Error('Token refresh timeout'));
      }, 10000); // 10 second timeout
    });

    // Start the refresh process
    this.refreshPromise = (async () => {
      try {
        // Clear any existing tokens to ensure a clean state
        if (typeof window !== 'undefined') {
          try {            // Clear session storage
            if (window.sessionStorage) {
              sessionStorage.removeItem('oss_auth_token_cache');
              sessionStorage.removeItem('oss_session');
            }// Clear local storage
            if (window.localStorage) {
              localStorage.removeItem('oss_auth_token');
              localStorage.removeItem('sb_auth_token');
            }
          } catch (clearError) {
            console.warn(`[${refreshId}] Error clearing token storage:`, clearError);
          }
        }

        // Use Supabase client directly
        const client = supabase;
        if (!client) {
          throw new Error('Supabase client not available');
        }

        // Get the current session
        const { data, error } = await client.auth.getSession();

        if (error || !data || !data.session) {
          console.warn(`[${refreshId}] No active auth session`);
          this.token = null;
          this.tokenExpiry = null;
          return null;
        }

        // Store the token
        this.token = data.session.access_token;

        // Validate token format
        const parts = this.token.split('.');
        if (parts.length !== 3) {
          console.error(`[${refreshId}] Invalid token format: token does not have 3 parts`);
          this.token = null;
          this.tokenExpiry = null;
          return null;
        }

        // Calculate expiry time (access tokens typically last 1 hour)
        const decodedToken = this._parseJwt(this.token);
        if (decodedToken && decodedToken.exp) {
          this.tokenExpiry = (decodedToken.exp * 1000) - (5 * 60 * 1000); // 5 minute buffer
          if (this.isDevMode) {
            console.log(`[${refreshId}] Token refreshed, valid until ${new Date(this.tokenExpiry).toISOString()}`);
          }
        } else {
          // Fallback: 55 minutes from now
          this.tokenExpiry = Date.now() + (55 * 60 * 1000);
          if (this.isDevMode) {
            console.log(`[${refreshId}] Token refreshed with fallback expiry (55 minutes)`);
          }
        }

        // Store token in sessionStorage only (not localStorage or cookies)
        // This is our standardized approach for token storage
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            sessionStorage.setItem('oss_auth_token_cache', JSON.stringify({
              token: this.token,
              expiry: this.tokenExpiry,
              refreshed: Date.now()
            }));
          } catch (storageError) {
            console.warn(`[${refreshId}] Error storing token in sessionStorage:`, storageError);
            // Continue even if storage fails
          }
        }

        return this.token;
      } catch (error) {
        console.error(`[${refreshId}] Error refreshing auth token:`, error);
        this.token = null;
        this.tokenExpiry = null;
        return null;
      } finally {
        // Always clean up resources
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        // Clear the promise reference
        this.refreshPromise = null;

        if (this.isDevMode) {
          console.log(`[${refreshId}] Token refresh process completed`);
        }
      }
    })();

    // Race the refresh promise against the timeout
    try {
      return await Promise.race([this.refreshPromise, timeoutPromise]);
    } catch (error) {
      // If the race fails, make sure we clean up properly
      console.error(`[${refreshId}] Token refresh race failed:`, error);
      this.refreshPromise = null;
      throw error;
    }
  }

  /**
   * Parse a JWT token to get expiration time
   *
   * @param {string} token - JWT token
   * @returns {Object|null} - Decoded token payload
   */
  _parseJwt(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return null;
    }
  }
}

// Export singleton instance
const apiClient = ApiClient.getInstance();
export default apiClient;

/**
 * Retrieves the current Supabase session with improved error handling and timeout protection.
 * This function is critical for obtaining the JWT token for authenticated API requests.
 * @returns {Promise<object|null>} The Supabase session object or null if no session exists.
 * @throws {AuthenticationError} If fetching the session fails.
 */
export const getSupabaseSession = async () => {
  // Generate a unique session ID for tracking
  const sessionId = Math.random().toString(36).substring(2, 8);

  // Set a timeout to prevent hanging indefinitely
  let timeoutId = null;
  const timeoutPromise = new Promise((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new AuthenticationError('Session fetch timeout'));
    }, 5000); // 5 second timeout
  });

  try {
    // Use Supabase client directly
    let client;
    try {
      // Use the Supabase client directly
      client = supabase;

      if (!client) {
        throw new AuthenticationError('Supabase client not available for getting session');
      }
    } catch (clientError) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      console.error(`[${sessionId}] Failed to get Supabase client:`, clientError);
      throw new AuthenticationError('Failed to initialize authentication client');
    }

    // Get session with timeout protection
    try {
      const sessionPromise = client.auth.getSession();
      const { data, error } = await Promise.race([sessionPromise, timeoutPromise]);

      // Clear the timeout since we got a response
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (error) {
        console.error(`[${sessionId}] Error getting Supabase session:`, error.message);
        throw new AuthenticationError(`Failed to get Supabase session: ${error.message}`);
      }

      if (!data || !data.session) {
        // This is not necessarily an error, but indicates no active session.
        // The caller should handle this case (e.g., redirect to login).
        console.warn(`[${sessionId}] No active Supabase session found.`);
        return null;
      }

      return data.session;
    } catch (sessionError) {
      console.error(`[${sessionId}] Error getting session:`, sessionError);

      if (sessionError instanceof AuthenticationError) {
        throw sessionError;
      }

      throw new AuthenticationError(`Failed to get authentication session: ${sessionError.message}`);
    }
  } catch (error) {
    // Clear the timeout to prevent memory leaks
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    console.error(`[${sessionId}] Exception in getSupabaseSession:`, error);

    if (error instanceof AuthenticationError) {
      throw error;
    }

    throw new AuthenticationError(`An unexpected error occurred while fetching the session: ${error.message}`);
  }
};
