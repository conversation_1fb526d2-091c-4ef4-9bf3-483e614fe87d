.main {
  width: 100%;
  margin: 0 auto;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Keep background fixed during scroll */
}

/* Section containers */
.sectionContainer {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.sectionTitle {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-color);
  position: relative;
  display: inline-block;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
}

.sectionSubtitle {
  font-size: 1.2rem;
  text-align: center;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto 3rem;
}

/* Service Cards Section */
.serviceCardsSection {
  padding: 5rem 0;
  text-align: center;
  background-color: rgba(248, 248, 248, 0.7); /* Translucent background */
  position: relative;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

/* Filter tabs - REMOVED */
/*
.filterTabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filterTab {
  padding: 0.8rem 1.5rem;
  border: 2px solid var(--primary-color);
  border-radius: 30px;
  background: transparent;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filterTab:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.activeTab {
  background-color: var(--primary-color);
  color: white;
}
*/

/* Cards grid */
.cardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-bottom: 3rem;
}

/* Service card */
.serviceCard {
  background-color: transparent;
  border-radius: 16px;
  height: 500px; /* Increased height to fit content without scrolling */
  perspective: 1000px; /* 3D effect */
  cursor: pointer;
  position: relative;
}

.cardContent {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.serviceCard:hover .cardContent {
  transform: rotateY(180deg);
}

/* Card front and back common styles */
.cardFront, .cardExpanded {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden; /* Safari */
  backface-visibility: hidden;
  border-radius: 16px;
  overflow: hidden;
}

/* Card front */
.cardFront {
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(5px);
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.serviceImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.serviceCard:hover .serviceImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  mix-blend-mode: multiply;
}

/* Service title overlay at bottom of image */
.serviceTitleOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  text-align: center;
}

.serviceTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Card expanded (back side) */
.cardExpanded {
  background-color: rgba(255, 255, 255, 0.9); /* Translucent white */
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Changed to space-between to push button to bottom */
  backdrop-filter: blur(8px); /* Add blur effect for better readability */
  transform: rotateY(180deg); /* Flip it */
  text-align: center; /* Center all text */
}

.serviceDescription {
  font-size: 0.95rem;
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.5;
  text-align: center;
  max-height: 5.5em; /* Limit height to prevent overflow */
  overflow: hidden;
}

.pricingTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--primary-color);
  text-align: center;
  margin-top: 0;
}

.pricingList {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem;
  width: 100%;
}

.pricingItem {
  display: flex;
  justify-content: space-between;
  padding: 0.6rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.pricingItem:last-child {
  border-bottom: none;
}

.pricingItemTitle {
  font-weight: 500;
  color: var(--text-color);
  text-align: left;
}

.pricingItemPrice {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

.bookButton {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  text-align: center;
  text-decoration: none;
  font-weight: 600;
  margin: 1rem auto 0;
  transition: all 0.3s ease;
  width: 80%;
  font-size: 1.1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.bookButton:hover {
  background-color: var(--secondary-color, #5562ff);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.closeExpanded {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
  color: var(--light-text-color);
  font-weight: 500;
  cursor: pointer;
}

/* Gallery Link Section */
.galleryLinkSection {
  padding: 6rem 0;
  text-align: center;
  background-color: rgba(78, 205, 196, 0.2); /* Slightly more visible pastel teal transparent */
  position: relative;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.galleryLinkContainer {
  margin-top: 3rem;
}

.galleryLink {
  display: inline-block;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color, #5562ff));
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.galleryLink:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
}

.serviceItem {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 4rem;
  padding-bottom: 4rem;
  border-bottom: 1px solid var(--border-color);
}

.serviceItem:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.serviceImage {
  flex: 1;
  min-width: 300px;
  height: 400px;
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.imagePlaceholder {
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
  border-radius: 8px;
}

.serviceImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.serviceContent {
  flex: 2;
  min-width: 300px;
}

.serviceContent h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.serviceContent p {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.pricing {
  background-color: var(--background-off-white); /* Light pastel for pricing section inside card */
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.pricing h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.pricing ul {
  list-style-type: none;
  padding: 0;
}

.pricing li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--light-text-color);
}

.pricing li:last-child {
  border-bottom: none;
}

.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

/* Booking info section */
.bookingInfo {
  background-color: rgba(248, 248, 248, 0.7); /* Translucent background */
  padding: 5rem 2rem;
  margin-bottom: 4rem;
  text-align: center;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.bookingInfo .sectionTitle {
  margin-bottom: 1rem;
  display: inline-block;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 3rem auto 0;
}

.infoCard {
  background-color: rgba(255, 255, 255, 0.85); /* Translucent white */
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: left;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.infoCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.infoCard h3 {
  font-size: 1.3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.infoCard p {
  color: var(--light-text-color);
  line-height: 1.6;
}

/* CTA section */
.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  padding: 5rem 2rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, rgba(85, 98, 255, 0.3) 100%);
  border-radius: 15px;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.cta h2 {
  font-size: 2.2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color, #5562ff));
  -webkit-background-clip: text;
  background-clip: text; /* Added standard property */
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.cta p {
  color: var(--light-text-color);
  margin-bottom: 2.5rem;
  font-size: 1.2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.outlineButton {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.outlineButton:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 992px) {
  .serviceItem {
    flex-direction: column;
  }

  .serviceImage {
    margin-right: 0;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .hero {
    padding: 4rem 1rem;
  }

  .services, .bookingInfo, .cta {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .serviceContent h2 {
    font-size: 1.8rem;
  }

  .serviceCard {
    height: 450px; /* Slightly smaller on tablets but still fits content */
  }

  .serviceTitle {
    font-size: 1.3rem;
  }

  .pricingTitle {
    font-size: 1.5rem;
  }

  .serviceDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
  }

  .button {
    width: 100%;
    text-align: center;
    margin-bottom: 1rem;
  }

  .serviceCard {
    height: 420px; /* Smaller on mobile but still fits all content */
  }

  .cardsGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .cardExpanded {
    padding: 1.5rem;
  }

  .pricingTitle {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
  }

  .serviceDescription {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .pricingList {
    margin-bottom: 1.5rem;
  }

  .pricingItem {
    padding: 0.6rem 0;
  }
}

/* Admin Panel Link */
.adminPanelLink {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.adminButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.adminButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
  text-decoration: none;
  color: white;
}

.adminEditButton {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.adminEditButton:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.errorContainer p {
  color: #666;
  margin-bottom: 2rem;
  max-width: 400px;
}

.retryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
